import constant from '@/utils/constant';
import { reportPV } from '@/utils/trackEvent/jdReport';
import { loadScript, parsePathByPattern } from '@/utils/client';
import { BaseInfo } from '@/types/BaseInfo';
import { ActivityStatus } from '@/types/ActivityStatus';
import { InitRequest } from '@/types/InitRequest';
import { InitResponse } from '@/types/InitResponse';

import CLIENT_TYPE, { getClientType, isPC } from '@/utils/platforms/clientType';
import CrmUtil from './products/crm';
import DOMAIN, { exchangeSubStr } from '@/utils/platforms/domain';
import { ActivityBaseInfo } from '@/types/ActivityBaseInfo';
import { isMiniProgram, wxSdkConfig } from '@/utils/platforms/wx';
import { getBeaconBehaviors, lzReportClick, reportDefaultValue } from './trackEvent/lzReport';
import { setThreshold } from '@/utils/setThreshold';
import { httpRequest } from '@/utils/service';
import { InitPreviewResponse } from '@/types/InitPreviewResponse';
import { removeTokenFromUrl } from '@/utils/platforms/auth';
import ClipboardTextPlugin from '@/plugins/ClipboardText';
import { DecoData } from '@/types/DecoData';
import { gotoErrorPage } from '@/utils/errorHandler';

import { Handler } from '@/utils/handle';

const handler = Handler.getInstance();

const isProd = process.env.NODE_ENV === 'production';

// 是否是预览页面
export const isPreview = window.location.pathname.includes('preview');

// 点击复制文案
export const clipboardText = ClipboardTextPlugin;

interface Info {
  activityType: string;
  templateCode: string;
}

// 检查调试状态
const checkDebug = (pathParams: KeyValue) => {
  if ('debug' in pathParams) {
    // 调试模式
    window.debug = true;
    if (window.VConsole) {
      const vc = new window.VConsole();
    }
  }
};

// pv埋点
const pv = (config: InitRequest, baseInfo: BaseInfo, pathParams: KeyValue) => {
  try {
    const { jdActivityId, jdActivityType, shopId } = baseInfo;

    window.sessionStorage.setItem(constant.LZ_JD_ACTIVITY_ID, jdActivityId);

    // 子午线pv
    reportPV({
      pin: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
      shopId,
      jdActivityId,
      jdActivityType,
      shareUserId: pathParams.shareUserId,
    }).then();
    // 陆泽埋点
    getBeaconBehaviors({
      ...reportDefaultValue(),
      sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
      opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID),
      at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE),
      uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
      e: 'enter',
    });
  } catch (e) {
    console.error(e);
  }
};

// 不支持的客户端
const notSupport = () => {
  // const clientType = getClientType();
  window.location.href = 'https://lzkjdz-isv.isvjcloud.com/prod/cc/custom/landing/openAppPage2/?actlink=' + encodeURIComponent(window.location.href);
  // // 如果是微信、QQ、PC环境
  // if (clientType === CLIENT_TYPE.WECHAT || clientType === CLIENT_TYPE.QQ) {
  //   console.log(`不支持的客户端${clientType}`, `${process.env.VUE_APP_HOST}/landing/miniProInfo/`);
  //   window.location.href = `${process.env.VUE_APP_HOST}/landing/miniProInfo/`;
  //   throw new Error('current client is not support');
  // }
  // const accessUrl = encodeURIComponent(window.location.href);
  // window.localStorage.setItem('actlink', accessUrl);
  // if (clientType === CLIENT_TYPE.M) {
  //   console.log(`不支持的客户端${clientType}`, `${process.env.VUE_APP_HOST}/landing/openAppPage2/?actlink=${accessUrl}`);
  //   window.location.href = `${process.env.VUE_APP_HOST}/landing/openAppPage2/?actlink=${accessUrl}`;
  //   throw new Error('current client is not support');
  // } else {
  //   console.log(`不支持的客户端${clientType}`, `${process.env.VUE_APP_HOST}/landing/openAppPage/?actlink=${accessUrl}`);
  //   window.location.href = `${process.env.VUE_APP_HOST}/landing/openAppPage/?actlink=${accessUrl}`;
  //   throw new Error('current client is not support');
  // }
};

// 前置检查器-更换域名
const clientInterceptor = (globalSetting: any) => {
  const clientType = getClientType();
  // 判断活动支持的客户端类型
  const { supportClientType } = globalSetting;
  console.log(`current clientType is ${clientType} support is`, supportClientType);
  // 如果supportClientType为空，支持所有客户端
  try {
    if (!supportClientType) return;

    // 否则配置哪个客户端哪个客户端可以访问
    if (supportClientType instanceof Array) {
      const supported = supportClientType.includes(clientType);
      if (!supported) {
        notSupport();
      }
    } else if (supportClientType instanceof String) {
      const supported = supportClientType === clientType;
      if (!supported) {
        notSupport();
      }
    }
  } catch (e) {
    console.error('clientInterceptor error', e);
  }
};

/**
 * 检查活动状态
 * @param baseInfo 活动信息
 * @param config 活动配置
 */
const statusInterceptor = (baseInfo: BaseInfo, config: InitRequest) => {
  const { showUnStartPage = false, showFinishedPage = false } = config;
  const { status } = baseInfo;
  if (status === ActivityStatus.notStarted && showUnStartPage) {
    console.log('未开始');
    window.location.href = `${process.env.VUE_APP_HOST}/common/not-start/?shopId=${baseInfo.shopId}&activityType=${baseInfo.activityType}&startTime=${baseInfo.startTime}&from=${encodeURIComponent(window.location.href)}`;
    // throw Error('活动尚未开始');
  } else if (status === ActivityStatus.finish && showFinishedPage) {
    console.log('已结束');
    window.location.href = `${process.env.VUE_APP_HOST}/common/finished/?shopId=${baseInfo.shopId}&activityType=${baseInfo.activityType}`;
    // throw Error('活动已经结束');
  } else if (status === ActivityStatus.noexit && showFinishedPage) {
    console.log('noexit');
    window.location.href = `${process.env.VUE_APP_HOST}/common/finished/?shopId=${baseInfo.shopId}&activityType=${baseInfo.activityType}`;
    // throw Error('活动不存在');
  }
};

// 检查参数
const checkParams = (params: { shopId: string; activityMainId: string }) => {
  if (!params.shopId) {
    throw new Error('店铺ID参数错误');
  }
  if (!params.activityMainId) {
    throw new Error('活动ID参数错误');
  }
};

// 获取变量
const getVariables = (config: InitRequest, pathParams: { [p: string]: string }) => {
  // 活动ID
  const activityMainId: string = config.activityMainId ?? pathParams.activityMainId;

  const templateCode = config.templateCode || pathParams.templateCode || '';

  // 活动类型
  const activityType = config.activityType || pathParams.activityType || '99';

  // venderId
  const shopId: string = config.shopId ?? pathParams.shopId;

  return {
    activityMainId: activityMainId.replace('#/', ''),
    activityType,
    templateCode,
    shopId,
  };
};

// 新增会员埋点
const newCustomTrackingEvent = () => {
  httpRequest.post('/common/add/member');
};

// 初始化前置检查
const beforeInit = async () => {
  try {
    const clientType = getClientType();
    const url = exchangeSubStr(window.location.href, DOMAIN.COMMON, DOMAIN.PROTECT);
    console.log('current domain is', clientType, url);

    // 判断是否需要切换域名
    const needSwitchDomain = window.location.href !== url && ((clientType === CLIENT_TYPE.WECHAT && !(await isMiniProgram())) || clientType === CLIENT_TYPE.QQ);

    if (needSwitchDomain) {
      window.location.href = url;
      // 跳转后无需继续执行
      return;
    }

    await wxSdkConfig();
  } catch (e) {
    console.error('beforeInit error:', e);
  }
};

// 登录(含模拟登录)
async function doLogin(pathParams: KeyValue, activityMainId: string, shopId: string, activityType: string, templateCode: string) {
  // 如果是模拟登录
  console.log('mockCode=========', pathParams.mockCode);
  // pathParams.mockCode = 'lzzk9wny3rai21b9oepjsot0j5eek8nd';
  if (pathParams.mockCode) {
    return await CrmUtil.mockLogin({
      activityMainId,
      activityType,
      templateCode,
      shopId,
      mockCode: pathParams.mockCode,
    });
  }
  // 获取用户pin
  return await CrmUtil.getPin({
    activityMainId,
    shopId,
    activityType,
    templateCode,
  });
}

// 跳转错误页面
export const goErrorPage = (code: any, message: any, errorPageUrl?: string) => {
  if (window.debug) {
    return;
  }
  window.location.href = errorPageUrl || `${process.env.VUE_APP_HOST}/common/500/?code=${code}&message=${encodeURIComponent(message)}`;
};

const isCPBActivity = ({ activityType, templateCode }: Info) => {
  const cppActivityPath = ['10015/2001', '10049/2001', '10109/2001', '10110/2001', '21003/1001', '21003/2001', '21004/1001', '30003/2001', '39001/2001', '90001/1001', '90001/2001', '90004/1001', '90006/1001', '90006/1002', '900010/1001', '92002/1001', '92002/1002'];
  return cppActivityPath.includes(`${activityType}/${templateCode}`);
};

const checkThreshold = async (info: Info) => {
  const { data }: any = await httpRequest.post('/common/getThreshold');
  data.thresholdResponseList = data.thresholdResponses;
  data.isCpb = isCPBActivity(info);
  if (info.activityType.toString() === '30006') {
    data.className = 'common-message-30006';
  }
  setThreshold({ ...data, ...info });
  if (data.showThreshold) {
    handler.trigger('onThresholdOpen').then();
  }
};

/**
 * 初始化页面
 * @param config 页面初始化配置
 * @returns InitResponse
 */
export const
  init = async (config: InitRequest): Promise<InitResponse> => {
  await beforeInit();
  console.group('init');
  const { urlPattern } = config;
  try {
    const pathParams: KeyValue = parsePathByPattern(urlPattern);
    const { activityMainId, activityType, templateCode, shopId } = getVariables(config, pathParams);

    // 检查参数,参数不正确会throw异常，中断代码
    checkParams({
      activityMainId,
      shopId,
    });

    // 检查调试状态
    checkDebug(pathParams);

    // 获取全局配置;
    const globalSetting = await CrmUtil.getGlobalSetting(shopId, activityMainId);

    // 前置检查
    clientInterceptor(globalSetting);

    // 店铺id
    sessionStorage.setItem(constant.LZ_SHOP_ID, shopId);

    // 活动id
    sessionStorage.setItem(constant.LZ_ACTIVITY_ID, activityMainId);

    // 活动类型
    sessionStorage.setItem(constant.LZ_ACTIVITY_TYPE, activityType);

    // 获取用户pin
    const userInfo = await doLogin(pathParams, activityMainId, shopId, activityType, templateCode);

    // 移除url中的token
    removeTokenFromUrl();

    // 获取活动的基础信息
    const activityBaseInfo: ActivityBaseInfo = await CrmUtil.getActivityBaseInfo();
    // 默认加载查询装修数据 可取消
    let decoData: DecoData = {};
    if (!config?.disableDecorate) {
      // 获取装修数据
      try {
        decoData = await CrmUtil.getActivityConfig();
      } catch (e) {
        decoData = {};
        console.error('没有装修数据');
      }
    }

    // 设置活动标题
    document.title = activityBaseInfo?.activityName;

    // 活动状态
    const status: ActivityStatus = CrmUtil.getActivityStatus({ ...activityBaseInfo });

    // 获取活动基础信息
    const baseInfo: BaseInfo = {
      ...activityBaseInfo,
      activityMainId,
      shopId,
      activityType,
      status,
    };

    // 检查活动状态
    statusInterceptor(baseInfo, config);

    // 检查活动门槛
    if (activityType && templateCode && !config.disableThreshold) {
      await checkThreshold({
        activityType,
        templateCode,
      });
    }

    // 如果是从开卡页面回来的，新增会员埋点
    pathParams.isJoin && newCustomTrackingEvent();

    // 获取大促公告
    if (!config.disableNotice) {
      await CrmUtil.getNotice();
    }

    // 分享配置
    if (!config.disableShare) {
      await CrmUtil.initShare(config.shareParams, activityMainId, pathParams);
    }

    // 记录pv数据上报（异步）
    pv(config, baseInfo, pathParams);

    // 从上一页返回时，重新获取数据
    if (config.backActRefresh) {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          window.location.reload();
          console.log('页面再次被展示');
        }
      });
    }

    return {
      userInfo,
      baseInfo,
      pathParams,
      decoData,
      // globalSetting,
    };
  } catch (error: any) {
    console.error('init error', error);
    isProd && goErrorPage(error.code, error.message, config.errorPageUrl);
    return Promise.reject(error);
  } finally {
    console.groupEnd();
  }
};

/**
 * 初始化页面
 * @param config 预览页面初始化配置
 * @returns InitResponse
 */
export const initPreview = async (config: InitRequest): Promise<InitPreviewResponse> => {
  const { urlPattern } = config;
  try {
    console.group('initPreview');
    const pathParams: KeyValue = parsePathByPattern(urlPattern);
    // 检查调试状态
    checkDebug(pathParams);
    const res = await CrmUtil.getConfig({
      id: pathParams.id,
      type: pathParams.type,
    });
    const activityData = res.activityData ? JSON.parse(res.activityData) : '';
    const decoData = res.decoData ? JSON.parse(res.decoData) : '';
    console.log('activityData', activityData);
    console.log('decoData', decoData);
    return {
      pathParams,
      activityData,
      decoData,
    };
  } finally {
    console.groupEnd();
  }
};

// 兼容旧版本
export const checkStatus = (baseInfo: BaseInfo, showUnStartPage: boolean, showFinishedPage: boolean) =>
  statusInterceptor(baseInfo, {
    showUnStartPage,
    showFinishedPage,
  });

export const initOnlyLogin = async (config: InitRequest) => {
  console.group('init');
  const { urlPattern } = config;
  sessionStorage.setItem('duration_time', new Date().getTime().toString());

  try {
    // 前置检查
    await beforeInit();

    const pathParams: { [propName: string]: string } = parsePathByPattern(urlPattern);

    const { activityMainId, activityType, shopId, templateCode } = getVariables(config, pathParams);

    // 检查调试状态
    checkDebug(pathParams);

    // 检查参数,参数不正确会throw异常，中断代码
    await checkParams({
      activityMainId,
      shopId,
    });

    // 店铺id
    sessionStorage.setItem(constant.LZ_SHOP_ID, shopId);
    // 活动id
    sessionStorage.setItem(constant.LZ_ACTIVITY_ID, activityMainId);
    // 活动类型
    sessionStorage.setItem(constant.LZ_ACTIVITY_TYPE, activityType);

    const userInfo = await doLogin(pathParams, activityMainId, shopId, activityType, templateCode);
    // 记录pv数据上报（异步）
    // 陆泽埋点
    getBeaconBehaviors({
      ...reportDefaultValue(),
      sid: sessionStorage.getItem(constant.LZ_SHOP_ID),
      opid: sessionStorage.getItem(constant.LZ_ACTIVITY_ID),
      at: sessionStorage.getItem(constant.LZ_ACTIVITY_TYPE),
      uid: window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) ?? '',
      e: 'enter',
    });

    // 从上一页返回时，重新获取数据
    if (config.backActRefresh) {
      document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'visible') {
          window.location.reload();
          console.log('页面再次被展示');
        }
      });
    }

    window.addEventListener('beforeunload', () => {
      console.log('页面离开');
      const durationTime = (new Date().getTime() - Number(sessionStorage.getItem('duration_time') ?? '0')) / 1000;
      lzReportClick({
        c: JSON.stringify({
          code: 'v_duration',
          value: durationTime,
        }),
        e: 'enter',
      });
    });

    return {
      userInfo,
      pathParams,
    };
  } catch (e: any) {
    console.error(e.message);
    if (e.message !== 'toLogin') {
      getBeaconBehaviors({
        c: JSON.stringify(e),
        e: 'error',
      });
      gotoErrorPage(0);
    }
    throw e;
  } finally {
    removeTokenFromUrl();
    console.groupEnd();
  }
};

export default {
  // 初始化
  init,
  initOnlyLogin,
  initPreview,
  // 获取客户端类型
  getClientType,
};
