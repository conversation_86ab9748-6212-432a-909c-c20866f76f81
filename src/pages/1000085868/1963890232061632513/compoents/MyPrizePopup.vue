<template>
  <VanPopup v-model:show="show">
    <div class="popup-container">
      <div class="prize-content">
        <div class="title">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/342217/3/1379/1741/68bfd409F580441b4/88b957dba125963a.png" alt="" />
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/342454/7/1349/1896/68bfd409F9a2667f1/e5824c966095835a.png" alt="" />
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/336558/3/8730/2029/68bfd409Fd98dc400/415c1e2934f80594.png" alt="" />
        </div>
        <div class="content">暂无领取记录</div>
      </div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/336699/33/7877/1518/68becc65F83003840/ce3f7cefdf3ea220.png" alt="" @click="close" />
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
});
const show = computed(() => props.showPopup);
const close = () => {
  emit('close');
};
const emit = defineEmits(['close']);
</script>
<style scoped>
.popup-container {
  width: 6.5rem;
  height: 9.2rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/348121/29/1375/10163/68bfd33eF36dbd3d5/8cc99ce1062e2e85.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding: 1.5rem 0.6rem 0.5rem;
  .prize-content {
    width: 100%;
    height: 6rem;
    font-size: 0.24rem;
    overflow-y: auto;
    .title {
      width: 100%;
      height: 0.9rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      img {
        width: 1.5rem;
      }
    }
    .content {
      padding-top: 0.2rem;
      width: 100%;
      text-align: center;
    }
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
