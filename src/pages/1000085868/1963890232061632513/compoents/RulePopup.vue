<template>
  <VanPopup v-model:show="show">
    <div class="popup-container">
      <div class="rule" v-html="rule"></div>
      <img class="close" src="//img10.360buyimg.com/imgzone/jfs/t1/336699/33/7877/1518/68becc65F83003840/ce3f7cefdf3ea220.png" alt="" @click="close" />
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  rule: {
    type: String,
    default: '',
  },
});
const show = computed(() => props.showPopup);
const close = () => {
  emit('close');
};
const emit = defineEmits(['close']);
</script>
<style scoped>
.popup-container {
  width: 6.5rem;
  height: 9.2rem;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/346401/14/1110/9932/68becb3bF9be0995e/14999a9517916304.png') no-repeat;
  background-size: 100%;
  position: relative;
  padding: 1.5rem 0.4rem 0.5rem;
  .rule {
    width: 100%;
    height: 6rem;
    font-size: 0.24rem;
    overflow-y: auto;
    white-space: wrap;
    word-break: break-word;
  }
  .close {
    width: 0.5rem;
    height: 0.5rem;
    position: absolute;
    bottom: 0.5rem;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
