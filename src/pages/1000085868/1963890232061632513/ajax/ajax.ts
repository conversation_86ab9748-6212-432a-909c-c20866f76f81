import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

// 活动主接口
export const mainActivity = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/doMain');
    closeToast();
    if (code !== 200) {
      showToast(message);
      return message;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 签到详情
export const signDetail = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    // });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/signDetail');
    if (code !== 200) {
      showToast(message);
      return message;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 点击签到
export const sign = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
    });
    const res = await httpRequest.post('/dz/1963890232061632513/sign');
    closeToast();
    if (res.code !== 200) {
      showToast(res.message);
      return false;
    }
    return res;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 获取活动规则
export const getRule = async () => {
  try {
    const { data, code, message } = await httpRequest.get('/common/getRule');
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 任务列表
export const taskList = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    // });
    const { data, code, message } = await httpRequest.post('/dz/1963890232061632513/task');
    if (code !== 200) {
      showToast(message);
      return false;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
