export interface MainData {
  /** 卡券总数 */
  cardTotal: number;
  /** 已使用卡券数 */
  cardUsed: number;
  /** 总数 */
  total: number;
  /** 已使用数 */
  used: number;
  /** 积分总数 */
  pointTotal: number;
  /** 已使用积分 */
  pointUsed: number;
  /** 是否弹出会员 */
  popMember: boolean;
}

/** 签到数据 */
export interface SignData {
  /** 是否已签到 */
  sign: boolean;
  /** 连续签到天数 */
  signDays: number;
  /** 签到记录列表 */
  signRecordList: [];
}

// 任务列表
/** 任务列表项接口 */
export interface TaskItem {
  /** 每日限制次数 */
  dailyLimit: number;
  /** 装饰信息 */
  decoration: Record<string, any>;
  /** 链接地址 */
  linkUrl: string;
  /** 抽奖次数 */
  lotteryCount: number;
  /** 订单结束时间 */
  orderEndTime: string;
  /** 订单开始时间 */
  orderStartTime: string;
  /** 赠送积分 */
  sendPoint: number;
  /** SKU ID列表 */
  skuIds: string;
  /** 排序ID */
  sortId: number;
  /** 任务结束时间 */
  taskEndTime: string;
  /** 已完成任务次数 */
  taskFinishCount: number;
  /** 任务开始时间 */
  taskStartTime: string;
  /** 任务类型 */
  taskType: number;
  /** 总限制次数 */
  totalLimit: number;
}
