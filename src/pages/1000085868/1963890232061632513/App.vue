<template>
  <div class="container">
    <img class="rule" src="//img10.360buyimg.com/imgzone/jfs/t1/349749/36/1000/3040/68bec430F9dab8a61/ed03d000d9145be6.png" alt="" @click="showPopup.showRulePopup = true" />
    <img class="prize" src="//img10.360buyimg.com/imgzone/jfs/t1/334533/18/10917/3168/68bec42fF9ccc6eb0/3a89da79a46f09bd.png" alt="" @click="showPopup.showMyPrizePopup = true" />
    <div class="btn-group">
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/338722/39/8982/3741/68c111d6F495e82df/9cd5ffe40bd566c1.png" alt="" @click="goHref('https://item.jd.com/100205007955.html')" />
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/332262/1/11570/3962/68c111d6F7703312a/646e425f2b300673.png" alt="" @click="goHref('https://item.jd.com/100205007959.html')" />
    </div>
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <div class="swiper-slide" v-for="(item, index) in swiperList" :key="index">
          <a :href="`#swiper-img-${index}`">
            <img class="swiper-img" :src="item.img" alt="" />
          </a>
        </div>
      </div>
    </div>
    <div class="calendar" id="swiper-img-0">
      <div class="calendar-header">
        <div class="month-display">
          <span style="font-size: 0.98rem">{{ currentMonth }}</span
          >月
        </div>
        <div class="month-selector">
          <img class="arrow" src="//img10.360buyimg.com/imgzone/jfs/t1/334114/20/11050/305/68bfbfb8Fbb276694/d34c3e3bc2846af4.png" alt="" @click="prevMonth" />
          <img class="arrow" src="//img10.360buyimg.com/imgzone/jfs/t1/323995/40/17637/335/68bed6e3F21222ed0/f650ab1a14f6ecba.png" alt="" @click="nextMonth" />
        </div>
        <div class="sign-info">
          <div class="sign-days">累计签到:&nbsp;&nbsp;{{ signData.signDays }} 天</div>
          <div class="sign-points">签到获得:&nbsp;&nbsp;{{ actData.pointTotal }}积分</div>
        </div>
        <div class="sign-btn" @click="signIn()">{{ isTodaySigned ? '已签到' : '点击签到' }}</div>
      </div>
      <div class="calendar-body">
        <div class="days">
          <div
            v-for="day in calendarDays"
            :key="day.date"
            class="day"
            :class="{
              hidden: !day.inMonth,
              today: day.isToday,
              signed: day.isSigned,
              activity: day.hasActivity,
            }"
            v-show="day.inMonth">
            <div class="day-number">{{ String(day.day).padStart(2, '0') }}</div>
            <div v-if="day.hasActivity" class="activity-tag">AAA活动</div>
            <div v-if="day.isSpecial" class="special-tag">品牌会员日</div>
          </div>
        </div>
      </div>
    </div>
    <img id="swiper-img-1" style="width: 7.5rem" src="//img10.360buyimg.com/imgzone/jfs/t1/350032/26/1395/119937/68bff321F73c91a0c/2eb95721e05e6b08.png" alt="" @click="goHref('https://item.jd.com/10181716483653.html')" />
    <img style="width: 7.5rem" src="//img10.360buyimg.com/imgzone/jfs/t1/333147/31/11076/108650/68bfd027Fb3fcfcdc/2025491af0e09b7d.jpg" alt="" @click="goHref('https://item.jd.com/10181717400407.html')" />
  </div>
  <RulePopup :showPopup="showPopup.showRulePopup" @close="showPopup.showRulePopup = false" :rule="rule.replaceAll('\n', '<br>')" />
  <MyPrizePopup :showPopup="showPopup.showMyPrizePopup" @close="showPopup.showMyPrizePopup = false" />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, nextTick } from 'vue';
import Swiper, { Autoplay } from 'swiper';
import 'swiper/swiper.min.css';
import { showToast } from 'vant';
import RulePopup from './compoents/RulePopup.vue';
import MyPrizePopup from './compoents/MyPrizePopup.vue';
import { mainActivity, signDetail, sign, getRule } from './ajax/ajax';
import { MainData, SignData } from './ajax/type';

// 将时间戳转换为日期字符串格式 YYYY-MM-DD
const formatTimestamp = (timestamp: number): string => {
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const swiperList = [
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/339786/15/8801/24013/68bff3b6F23b249d5/22d472e6e7dfc68b.png',
  },
  {
    img: '//img10.360buyimg.com/imgzone/jfs/t1/328914/40/18194/25410/68bff193F2b8b1ea7/35e0a8d2ebfb1093.png',
  },
];
// 活动数据
const actData = ref<MainData>({
  cardTotal: 0,
  cardUsed: 0,
  total: 0,
  used: 0,
  pointTotal: 0,
  pointUsed: 0,
  popMember: false,
});
const signData = ref<SignData>({
  sign: false,
  signDays: 0,
  signRecordList: [],
});
const showPopup = reactive({
  showRulePopup: false,
  showMyPrizePopup: false,
});
const rule = ref('');
// 日历相关数据
const currentDate = new Date();
const currentMonth = ref(currentDate.getMonth() + 1); // 当前月份，从0开始，所以+1
const currentYear = ref(currentDate.getFullYear()); // 当前年份

// 特殊日期配置
const activityDates = [
  '2023-09-11',
  '2023-09-12', // 9月活动日
  '2023-10-11',
  '2023-10-12', // 10月活动日
];
const specialDates = [
  '2023-09-08', // 9月品牌会员日
  '2023-10-08', // 10月品牌会员日
];

// 计算日历天数
const calendarDays = ref<any[]>([]);
// 判断今日是否已签到
const isTodaySigned = ref(false);

// 生成日历数据
const generateCalendar = () => {
  const days = [];
  const lastDay = new Date(currentYear.value, currentMonth.value, 0);
  // 只填充当月的日期
  let startDay = 1;
  if (currentMonth.value === 9) {
    startDay = 8; // 九月从8号开始展示
  }
  for (let i = startDay; i <= lastDay.getDate(); i++) {
    const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
    const today = new Date();
    const isToday = today.getDate() === i && today.getMonth() === currentMonth.value - 1 && today.getFullYear() === currentYear.value;
    days.push({
      day: i,
      date: dateStr,
      inMonth: true,
      isToday,
      isSigned: signData.value.signRecordList.some((record) => {
        const recordDate = formatTimestamp(Number(record));
        return recordDate === dateStr;
      }),
      hasActivity: activityDates.includes(dateStr),
      isSpecial: specialDates.includes(dateStr),
    });
  }
  calendarDays.value = days;

  // 更新今日是否已签到状态
  const today = new Date();
  const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
  isTodaySigned.value = signData.value.signRecordList.some((record) => {
    const recordDate = formatTimestamp(Number(record));
    return recordDate === todayStr;
  });
};

// 切换到上个月
const prevMonth = () => {
  if (currentMonth.value === 9) {
    currentMonth.value = 10;
  } else {
    currentMonth.value = 9;
  }
  generateCalendar();
};

// 切换到下个月
const nextMonth = () => {
  if (currentMonth.value === 10) {
    currentMonth.value = 9;
  } else {
    currentMonth.value = 10;
  }
  generateCalendar();
};
// 活动信息
const activityInfo = async () => {
  actData.value = await mainActivity();
};
// 签到详情
const getSignDetail = async () => {
  signData.value = await signDetail();
};
// 签到功能
const signIn = async () => {
  try {
    const res = await sign();
    if (res && res.code === 200) {
      // 重新获取签到详情
      activityInfo().then(() => {
        // 确保获取到签到详情后再更新日历显示
        getSignDetail().then(() => {
          showToast('签到奖励300积分，累积积分可兑换千元好礼！');
          generateCalendar();
        });
      });
    }
  } catch (error) {
    showToast('签到失败，请稍后重试');
  }
};

Swiper.use([Autoplay]);
let mySwiper: Swiper;
const initSwiper = () => {
  mySwiper = new Swiper('.swiper-container', {
    autoplay: true,
    slidesPerView: 2,
    // spaceBetween: 20,
  });
};
const goHref = (url: string) => {
  window.location.href = url;
};
onMounted(async () => {
  nextTick(() => {
    initSwiper();
  });
  if (currentMonth.value !== 9 && currentMonth.value !== 10) {
    currentMonth.value = 9; // 如果当前不是9月或10月，默认显示9月
  }
  await activityInfo();
  await getSignDetail().then(() => {
    // 初始化日历，默认显示当前月份
    generateCalendar();
  });
  rule.value = await getRule();
});
</script>
<style scoped>
.container {
  width: 7.5rem;
  min-height: 100vh;
  background: url('//img10.360buyimg.com/imgzone/jfs/t1/336409/40/8392/27691/68bec431Ff29b88f3/5d7366cb65227148.jpg') no-repeat;
  background-size: 100%;
  position: relative;
  padding-top: 7rem;
  .rule {
    width: 1.33rem;
    position: absolute;
    top: 0.72rem;
    right: 0;
  }
  .prize {
    width: 1.33rem;
    position: absolute;
    top: 1.5rem;
    right: 0;
  }
  .btn-group {
    width: 100%;
    display: flex;
    justify-content: space-around;
    position: absolute;
    top: 6rem;
    padding: 0 0.5rem;
    img {
      width: 2.7rem;
    }
  }
  .swiper-container {
    width: 7rem;
    margin: 0 auto;
    overflow: hidden;

    .swiper-wrapper {
      width: 7rem;
      .swiper-slide {
        width: 3.15rem;
        .swiper-img {
          width: 100%;
        }
      }
    }
  }
  .calendar {
    width: 7rem;
    height: 6rem;
    background: url('//img10.360buyimg.com/imgzone/jfs/t1/350025/15/1392/24269/68bff193Fc3ad0981/979508e7c442f3ad.png') no-repeat;
    background-size: 100% 100%;
    margin: 0.4rem auto 0;
    padding: 0.3rem 0.2rem;
    border-radius: 0.2rem;
    box-sizing: border-box;
    overflow: hidden;

    .calendar-header {
      height: 1.5rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.1rem 0 0.1rem 0.2rem;
      .month-display {
        width: 1.5rem;
        font-size: 0.32rem;
        font-weight: bold;
        color: #6ac5ff;
        margin: 0.05rem 0;
        text-align: center;
      }
      .month-selector {
        display: flex;
        height: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        width: 0.8rem;

        .arrow {
          width: 0.4rem;
          cursor: pointer;
        }
      }

      .sign-info {
        flex: 1;
        text-align: left;
        padding-left: 0.2rem;

        .sign-days,
        .sign-points {
          font-size: 0.22rem;
          color: #666;
          margin: 0.03rem 0;
        }
      }

      .sign-btn {
        width: 1.8rem;
        height: 0.7rem;
        line-height: 0.7rem;
        text-align: center;
        background: linear-gradient(to right, #4bb5fd, #6fcbff);
        color: white;
        border-radius: 0.35rem;
        font-size: 0.26rem;
        cursor: pointer;
      }

      .sign-btn.signed {
        background: linear-gradient(90deg, #cccccc, #999999);
        cursor: not-allowed;
      }
    }

    .calendar-body {
      margin-top: 0.2rem;
      height: 2.5rem;
      overflow: auto;

      .days {
        display: flex;
        flex-wrap: wrap;

        .day {
          width: 0.88rem;
          height: 1.21rem;
          margin-left: 0.04rem;
          margin-bottom: 0.04rem;
          background-color: #ffffff;
          border-radius: 0.1rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          position: relative;

          .day-number {
            font-size: 0.34rem;
            color: #333;
          }

          &.hidden {
            display: none;
          }

          &.today {
            border: solid 0.04rem #4460dc;
          }

          &.signed {
            background-color: #91cbf6;
            border: none;
            .day-number {
              color: #fff;
              margin-top: -0.5rem;
            }
          }

          .activity-tag,
          .special-tag {
            font-size: 0.16rem;
            padding: 0.02rem 0.05rem;
            border-radius: 0.05rem;
            margin-top: 0.05rem;
            white-space: nowrap;
            transform: scale(0.8);
          }

          .activity-tag {
            background-color: #ff6b6b;
            color: white;
          }

          .special-tag {
            background: linear-gradient(to right, #ff6b6b, #ffb199);
            color: white;
          }
        }
      }
    }

    .calendar-footer {
      margin-top: 0.1rem;
      padding: 0.1rem 0;

      .sign-desc {
        font-size: 0.22rem;
        color: #666;
        line-height: 1.2;
      }
    }
  }
}
</style>
<style>
::-webkit-scrollbar {
  display: none;
}
</style>
