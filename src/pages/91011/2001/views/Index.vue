<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-kv">
      <img
        :src="
          furnish.actBg ??
          'https://img10.360buyimg.com/imgzone/jfs/t1/301137/31/18237/686303/68622ef5F2950375e/5708dba47e433889.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value">
          <!--{{ shopName }}-->
        </div>
        <div>
          <div
            class="header-btn"
            v-click-track="'hdgz'"
            :style="furnishStyles.headerBtnRules.value"
            @click="showRulePopup"
          />
          <div
            class="header-btn"
            v-click-track="'wdjp'"
            :style="furnishStyles.headerBtnMyPrizes.value"
            @click="showMyPrizePop"
          />
        </div>
      </div>
    </div>
    <!-- 权益1 -->
    <div
      class="qyDivOneAll"
      v-click-track="'hyqy'"
      :style="{ backgroundImage: `url(${furnish.qy1Bg})` }"
    >
      <!-- 立即领取按钮如果符合首购条件则显示，否则置灰 -->
      <div
        v-if="prizeList1 && prizeList1[0] && prizeList1[0].status === 2"
        class="getQyBtn"
        @click="getQyClick(1, prizeList1[0])"
      >
        立即领取
      </div>
      <div
        v-else-if="prizeList1 && prizeList1[0] && prizeList1[0].status === 4"
        class="getQyBtn"
        @click="getQyClick(1, prizeList1[0])"
      >
        立即领取
      </div>
      <div
        v-else-if="prizeList1 && prizeList1[0] && prizeList1[0].status === 1"
        class="getQyBtn getQyBtnGray"
        @click="getQyClick(1, prizeList1[0])"
      >
        已领取
      </div>
      <div
        v-else-if="prizeList1 && prizeList1[0] && prizeList1[0].status === 3"
        class="getQyBtn getQyBtnGray"
        @click="getQyClick(1, prizeList1[0])"
      >
        立即领取
      </div>
      
    </div>

    <!-- 权益2 -->
    <div class="qyDivTwoAll">
      <img :src="furnish.qy2Bg" alt="" />
      <div class="qyTwoListDiv">
        <div
          class="qyTwoItemDiv"
          v-for="(it, index) in furnish.qy2BgItemBgArr"
          :key="index"
          :style="{ backgroundImage: `url(${it.skuImg})` }"
        >
          <!-- qyTwoBtnGray -->
          <div
            :class="[
              prizeList2 && prizeList2[index] && prizeList2[index].status === 2
                ? 'qyTwoBtn'
                : 'qyTwoBtnGray',
            ]"
            @click="getQyClick(2, prizeList2[index])"
          >
            立即领取
          </div>
        </div>
      </div>
    </div>

    <div class="qyDivThreeAll">
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="6.88" :data="furnish.hotZoneSetting" reportKey="" />
    </div>

    <div class="sku" v-if="skuList.length">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="" />
      <div class="sku-list">
        <div
          class="sku-item"
          v-for="(item, index) in skuList"
          :key="index"
          v-click-track="{ code: 'ljgm', value: item.skuId }"
          @click="gotoSkuPage(item.skuId)"
        ></div>
      </div>
    </div>

    <!-- 活动门槛 -->
    <Threshold
      :showPopup="showLimit"
      @closeDialog="showLimit = false"
      :canNotCloseJoin="furnish.canNotCloseJoinPopup"
      :data="baseInfo?.thresholdResponseList"
    />
    <!-- 非会员拦截 -->
    <OpenCard
      :showPopup="showOpenCard"
      @closeDialog="showOpenCard = false"
      :canNotCloseJoin="furnish.canNotCloseJoinPopup"
    />
    <!-- 规则 -->
    <VanPopup teleport="body" v-model:show="showRule">
      <RulePopup :rule="ruleTest" @close="showRule = false" />
    </VanPopup>
    <!--我的奖品-->
    <VanPopup teleport="body" v-model:show="showMyPrize">
      <MyPrize
        v-if="showMyPrize"
        @close="showMyPrize = false"
        @showCardNum="null"
        @savePhone="null"
      />
    </VanPopup>
    <!-- 保存地址弹窗 -->
    <VanPopup teleport="body" v-model:show="showSaveAddress">
      <SaveAddress
        v-if="showSaveAddress"
        :userReceiveRecordId="userReceiveRecordId"
        @close="showSaveAddress = false"
        :echoData="echoData"
      />
    </VanPopup>
  </div>
</template>
<script setup lang="ts">
import { computed, inject, reactive, ref, watchEffect } from "vue";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { closeToast, showLoadingToast, showToast } from "vant";
import { httpRequest } from "@/utils/service";
import { DecoData } from "@/types/DecoData";
import { BaseInfo } from "@/types/BaseInfo";
import { callShare } from "@/utils/platforms/share";
import { gotoSkuPage } from "@/utils/platforms/jump";
import constant from "@/utils/constant";
import RulePopup from "../components/RulePopup.vue";
import Threshold from "../components/Threshold.vue";
import GiftConfirm from "../components/GiftConfirm.vue";
import OpenCard from "../components/OpenCard.vue";
import dayjs from "dayjs";
import MyPrize from "../components/MyPrize.vue";
import HotZone from "../components/HotZone.vue";
import SaveAddress from "../components/SaveAddress.vue";

const decoData = inject("decoData") as DecoData;
const baseInfo: BaseInfo = inject("baseInfo") as BaseInfo;
const endTime = ref(0);
const isStart = ref(false);
const isEnd = ref(false);
const startTime = ref(0);
// 保存实物地址相关
const showSaveAddress = ref(false);
const userReceiveRecordId = ref("");
const echoData = {
  realName: "",
  mobile: "",
  province: "",
  city: "",
  county: "",
  address: "",
};
const getTime = () => {
  startTime.value = new Date(baseInfo.startTime).getTime();
  endTime.value = new Date(baseInfo.endTime).getTime();
  const now = new Date().getTime();
  if (now > startTime.value) {
    isStart.value = true;
  }
  if (now < startTime.value) {
    isStart.value = false;
  }
  if (now > endTime.value) {
    isEnd.value = true;
  }
  if (now < endTime.value) {
    isEnd.value = false;
  }
};
// 是否是新客 1新客 2老客
const authStatus = ref(1);

// 门槛弹窗
const showOpenCard = ref(false);
// 规则弹窗
const showRule = ref(false);
// 活动规则
const ruleTest = ref("");
// 我的奖品弹窗
const showMyPrize = ref(false);
// 单次领取弹窗
const isShowConfirmPopup = ref(false);

// 奖品列表
const prizeList1 = ref<any>([]); // 权益一奖品列表
const prizeList2 = ref<any>([]); // 权益二奖品列表

const giftItem = ref({});
// 活动商品列表
type Sku = {
  skuId: string;
  skuName: string;
  skuMainPicture: string;
  jdPrice: string;
};
const skuList = ref<Sku[]>([]);

// 展示门槛显示弹框
const showLimit = ref(false);

// 活动规则相关
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get("/common/getRule");
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};
const showMyPrizePop = () => {
  showMyPrize.value = true;
};

// 主接口获取信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post("/91011/activity");
    // 判断是否开启新客跳老客的逻辑，如果开启并且不是新客则跳转链接
    // 1新客 2老客
    authStatus.value = data.authStatus;
    if (authStatus.value === 2) {
      // 如果是老客
      if (furnish.isShowJump && furnish.jumpUrl) {
        window.location.href = furnish.jumpUrl;
        return;
      }
    }
    prizeList1.value = data.prizeList.filter((item: any) => item.equityType === 1);
    prizeList2.value = data.prizeList.filter((item: any) => item.equityType === 2);
    console.log(prizeList1.value, prizeList2.value, "data*********");
  } catch (error) {
    console.error(error);
  }
};

// 获取曝光商品
const getSkuList = async () => {
  try {
    const { data } = await httpRequest.post("/91011/getExposureSku");
    console.log(data, "曝光商品");
    skuList.value = data;
  } catch (error) {
    console.error(error);
  }
};
// 奖品领取
const getQyClick = async (qyType: any, itemData: any) => {
  // console.log(itemData, prizeList1.value, prizeList2.value, "itemData============");
  if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
    showOpenCard.value = true;
    return;
    // console.log("非会员");
  } else if (
    baseInfo.thresholdResponseList[0]?.thresholdCode === 1 ||
    baseInfo.thresholdResponseList[0]?.thresholdCode === 2 ||
    baseInfo.thresholdResponseList[0]?.thresholdCode === 1501
  ) {
    showLimit.value = true;
    return;
  }
  if (itemData.status === 1) {
    showToast("您已经领取过了");
    return;
  }
  if (itemData.status === 3) {
    showToast("您不符合参与条件");
    return;
  }
  if (itemData.status === 4) {
    showToast("奖品已发光");
    return;
  }
  
  try {
    showLoadingToast({
      message: "加载中...",
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post("/91011/receivePrize", {
      prizeId: itemData.prizeId,
    });
    console.log(data, "奖品领取=======");
    if (data && data.result && data.result.prizeType === 3) {
      closeToast();
      // 实物
      showSaveAddress.value = true;
      userReceiveRecordId.value = data.result.userPrizeId;
      getActivityInfo();
    } else {
      showToast({
        message: "领取成功",
        duration: 2000,
        forbidClick: true,
        onClose: () => {
          getActivityInfo();
        },
      });
    }
  } catch (error: any) {
    showToast({
      message: error.message,
      duration: 2000,
      forbidClick: true,
      onClose: () => {
        getActivityInfo();
      },
    });
  }
};
// 初始化
const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    getTime();
    await Promise.all([getActivityInfo(), getSkuList()]);
    if (baseInfo.thresholdResponseList[0]?.thresholdCode === 4) {
      showOpenCard.value = true;
      console.log("非会员");
    } else if (
      baseInfo.thresholdResponseList[0]?.thresholdCode === 1 ||
      baseInfo.thresholdResponseList[0]?.thresholdCode === 2 ||
      baseInfo.thresholdResponseList[0]?.thresholdCode === 1501
    ) {
      showLimit.value = true;
    }
  } catch (error) {
    console.log(error);
  }
};

// 确认领取弹窗的回调
const drawSuccessFun = async () => {
  isShowConfirmPopup.value = false;
  await init();
};
watchEffect(() => {
  // 收集依赖
  if (baseInfo.startTime === dayjs().unix() * 1000) {
    window.location.reload();
  }
});

const toInit = async () => {
  showLoadingToast({
    message: "加载中...",
    forbidClick: true,
    duration: 0,
  });
  await init();
  closeToast();
};
toInit();
</script>

<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.2rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.37rem;
    height: 0.57rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.qyDivOneAll {
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/301639/17/19105/37030/68622ef8Ff01ded9c/f25480c8ea22c91e.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.88rem;
  height: 3.2rem;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  .getQyBtn {
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/231343/9/29061/1599/675f9872F87a87c9c/0e76d2742d53fd96.jpg)
      no-repeat;
    background-size: 100%;
    width: 0.8rem;
    height: 1.78rem;
    display: flex;
    align-items: center;
    color: #fff;
    text-align: center;
    font-size: 0.28rem;
    padding: 0.1rem 0.2rem 0.1rem 0.1rem;
    box-sizing: border-box;
    position: absolute;
    right: 0.25rem;
    top: 1.1rem;
  }
  .getQyBtnGray {
    filter: grayscale(1);
  }
}
.qyDivTwoAll {
  width: 6.88rem;
  margin: 0.3rem auto;
  position: relative;
  padding-bottom: 0.2rem;
  min-height: 3.2rem;
  img {
    position: absolute;
    width: 6.88rem;
  }
  .qyTwoListDiv {
    position: relative;
    // top: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    padding-top: 1rem;
    .qyTwoItemDiv {
      position: relative;
      background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/310318/36/17401/36053/6875ff9fF7a78bd80/52e1449f20c0651d.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
      width: 6.26rem;
      height: 1.6rem;
      margin-bottom: 0.1rem;
      .qyTwoBtn {
        position: absolute;
        background: #c56f26;
        width: 1.8rem;
        height: auto;
        font-size: 0.28rem;
        line-height: 1;
        padding: 0.1rem 0.25rem;
        border-radius: 0.3rem;
        color: #fff;
        left: 3.15rem;
        top: 0.9rem;
      }
      .qyTwoBtnGray {
        position: absolute;
        background: #c56f26;
        width: 1.8rem;
        height: auto;
        font-size: 0.28rem;
        line-height: 1;
        padding: 0.1rem 0.25rem;
        border-radius: 0.3rem;
        color: #fff;
        left: 3.15rem;
        top: 0.9rem;
        filter: grayscale(1);
      }
    }
  }
}
.qyDivThreeAll {
  margin-top: 0.6rem;
}
.sku {
  width: 7.21rem;
  padding: 0.2rem 0;
  position: relative;
  margin: 0.4rem auto 0.1rem auto;
  position: relative;
  // background-color: #000;
  .sku-list-img {
    width: 7.21rem;
    height: auto;
    position: absolute;
    top: 0;
    // background-color: #f2270c;
  }
  .sku-list {
    width: 7.25rem;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-gap: 0.2rem 0.2rem;
    // background-color: aqua;
    position: relative;
    z-index: 1;
    .sku-list1 {
      padding-top: 0.9rem;
      width: 7.25rem;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: auto;
      grid-gap: 0.2rem 0.2rem;
      // background-color: #c56f26;
    }
  }
  .sku-item {
    width: 3.3rem;
    height: 4rem;
    overflow: hidden;
    margin-bottom: 0.2rem;
    .sku-text {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        width: 1.4rem;
        height: 0.3rem;
        //background-color: #000;
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
