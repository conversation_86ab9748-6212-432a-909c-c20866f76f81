<template>
  <div class='bk'>
    <div class='close'><img src='../assets/close.png' alt='' @click='close' /></div>

    <div class='handle-btn' @click='gotoShopPage(baseInfo.shopId)'></div>

  </div>
</template>

<script lang='ts' setup>
import { inject, ref } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';
import { BaseInfo } from '@/types/BaseInfo';

const baseInfo: any = inject('baseInfo') as BaseInfo;

const emits = defineEmits(['close'])
const close = () => {
  emits('close');
};
</script>

<style scoped lang='scss'>
.bk {
  width: 7rem;
  height: 7.97rem;
  position: relative;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/330404/38/1811/54229/68a6b681F27179039/6ab498148c18cf7d.jpg");
    repeat: no-repeat;
    size: contain;
  };

  .close {
    padding: 0.3rem 0.3rem 0.2rem 0;
    text-align: right;

    img {
      display: inline-block;
      width: 0.25rem;
    }
  }

  .handle-btn {
    width: 3.5rem;
    height: 1rem;
    position: absolute;
    left: 50%;
    bottom: .6rem;
    transform: translateX(-50%);
  }
}
</style>
