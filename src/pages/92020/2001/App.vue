<template>
  <div class='bg' :style='furnishStyles.pageBg.value'>
    <div class='header-content'>
      <div class='shop-name' :style='furnishStyles.shopNameColor.value'>
        <span v-if='furnish.disableShopName === 1'>{{ shopName }}</span>
      </div>
    </div>
    <div class='exchange-area' :style='furnishStyles.giftImg.value'>
      <div class='btn' v-if='actData.status === 3 || actData.status === 2' @click='toLock'>立即参与 ></div>
      <div class='btn' v-else-if='actData.status === 0 || actData.status === 1' @click='getPrize'>已锁权 兑好礼 ></div>

      <div class='btn-list'>
        <div :style='furnishStyles.headerBtn.value' @click='rulePopup = true'>活动规则</div>
        <div>&nbsp;|&nbsp;</div>
        <div :style='furnishStyles.headerBtn.value' @click='recordPopup = true'>锁权记录</div>
      </div>
    </div>

    <div class='suspend-view' v-if='furnish.isShowSuspend'>
      <img :src='furnish?.suspendImg' @click='gotoUrlLink(furnish.suspendLink)' alt=''>
    </div>

    <div class='sku-view' :style='{backgroundImage:`url(${furnish.skuModuleBg})`}' v-if='skuList?.length>0'>
      <div class='sku-list' :style="{height:skuList?.length<=2?'4.1rem':'8.5rem'}">
        <div class='sku-item' :style='{backgroundImage:`url(${furnish.skuItemBg})`}' v-for='item in skuList' :key='item.skuId'>
          <div class='sku-img-view'>
            <img :src='item.skuMainPicture' alt='' class='sku-img' />
          </div>
          <div class='sku-name two-line-omit'>{{ item.skuName }}</div>
          <div class='sku-btn'>
            <div class='price'><span style='font-size: .18rem'>￥</span>{{ item.jdPrice }}</div>
            <div class='buy' @click='gotoSkuPage(item.skuId)' :style='{backgroundColor:furnish.skuBtnColor}'>
              <img style='width: .35rem' src='//img10.360buyimg.com/imgzone/jfs/t1/327187/8/8601/655/68a6d7b9Ff2cdbfa3/b3ee0e53d72ef030.png' alt=''>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <van-popup v-model:show='rulePopup' :closeOnClickOverlay='false'>
    <PopupBk @close='rulePopup = false'>
      <Rule></Rule>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='dissatisfiedPopup'>
    <Dissatisfied @close='dissatisfiedPopup = false'></Dissatisfied>
  </van-popup>
  <van-popup v-model:show='lockingPopup' :closeOnClickOverlay='false'>
    <PopupBk @close='lockingPopup = false'>
      <Locking @close='lockingPopup = false' @success='lockingSuccess'></Locking>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='recordPopup' :closeOnClickOverlay='false'>
    <PopupBk v-if='recordPopup' @close='recordPopup = false'>
      <Record :tab='activeTab'></Record>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='addressPopup' :closeOnClickOverlay='false'>
    <PopupBk @close='addressPopup = false'>
      <SaveAddress @close='addressPopup = false'></SaveAddress>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='successPopup' :closeOnClickOverlay='false'>
    <PopupBk @close='successPopup = false'>
      <Success @showAddress='showAddress'></Success>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='failPopup' :closeOnClickOverlay='false'>
    <PopupBk @close='failPopup = false'>
      <Fail></Fail>
    </PopupBk>
  </van-popup>
  <Threshold :showPopup="showLimit" @closeDialog="showLimit = false" :data="baseInfo?.thresholdResponseList" />

</template>

<script lang='ts' setup>
import { inject, ref } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import usePostMessage from '@/hooks/usePostMessage';
import { closeToast, showLoadingToast, showToast } from 'vant';
import PopupBk from './components/PopupBk.vue';
import Rule from './components/Rule.vue';
import Dissatisfied from './components/Dissatisfied.vue';
import Locking from './components/Locking.vue';
import Record from './components/Record.vue';
import SaveAddress from './components/SaveAddress.vue';
import Success from './components/Success.vue';
import Fail from './components/Fail.vue';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';
import { actData, getActData } from './ts/logic';
import Threshold from '@/components/Threshold/index.vue';
import { checkThreshold } from '@/components/Threshold/ts/logic';
import { httpRequest } from '@/utils/service';
import { gotoSkuPage } from '@/utils/platforms/jump';


const { registerHandler } = usePostMessage();

const decoData = inject('decoData') as DecoData;
const baseInfo: any = inject('baseInfo') as BaseInfo;
const shopName = ref(baseInfo.shopName);
const rulePopup = ref(false);
const dissatisfiedPopup = ref(false);
const lockingPopup = ref(false);
const recordPopup = ref(false);
const activeTab = ref(1);
const addressPopup = ref(false);
const successPopup = ref(false);
const failPopup = ref(false);
const showLimit = ref(false);

const gotoUrlLink = (link: string) => {
  if (link) {
    window.location.href = link;
  }
};
//
// const checkThreshold = () => {
//   if (baseInfo.thresholdResponseList.length) {
//     const list = baseInfo?.thresholdResponseList;
//     const s = baseInfo.thresholdLevelsStatus;
//
//     if (list?.findIndex((i: any) => i.thresholdCode === 1) > -1) {
//       showToast('活动未开始');
//       return false;
//     }
//     if (list?.findIndex((i: any) => i.thresholdCode === 2) > -1) {
//       showToast('活动已结束');
//       return false;
//     }
//
//     if (s === 1) {
//       showToast('您不是此活动参与的指定人群');
//       return false;
//     } else if (s === 2) {
//       showToast('您不是付费会员，无法参加活动');
//       return false;
//     } else if (s === 3) {
//       showToast('您当前不是会员，无法参加活动');
//       return false;
//     } else if (s === 4) {
//       showToast('未达到会员等级要求无法参加活动');
//       return false;
//     } else if (s === 5) {
//       showToast('未关注店铺并且不是会员无法参加活动');
//       return false;
//     }
//   }
//   return true;
// };


const toLock = () => {
  if (!checkThreshold(baseInfo)) return;
  if (dayjs().isBefore(actData.value.powerStartTime)) {
    showToast('锁权未开始');
    return;
  }
  if (dayjs().isAfter(actData.value.powerEndTime)) {
    showToast('锁权已结束');
    return;
  }
  if (actData.value.userPoints < actData.value.points) {
    showToast('积分不足');
    return;
  }
  addressPopup.value = true;
};
const lockingSuccess = () => {
  addressPopup.value = false;
};

const showAddress = () => {
  successPopup.value = false;
  activeTab.value = 2;
  recordPopup.value = true;
  setTimeout(() => {
    activeTab.value = 1;
  }, 1000);
};

const getPrize = async () => {
  try {
    if (actData.value.status === 1) {
      successPopup.value = true;
      return;
    }
    showLoadingToast({
      message: '领取中...',
      forbidClick: true,
      duration: 0,
    });
    const re = await httpRequest.post('/92020/checkOrder', {
      orderIds: [],
    });
    const res = await httpRequest.post('/92020/sendPrize');
    await getActData();
    closeToast();
    if (res.data.status === 1) {
      successPopup.value = true;
    } else if (res.data.cause === '奖品总数量不足') {
      failPopup.value = true;
    } else {
      showToast('领取失败，请联系客服');
    }
  } catch (error: any) {
    closeToast();
    if (error.message === '奖品总数量不足') {
      failPopup.value = true;
    } else if (error.message === '订单不存在' || error.message === '订单数量不足' || error.message === '订单金额不足' || error.message === '未到领奖时间' || error.message === '订单不满足领奖状态') {
      dissatisfiedPopup.value = true;
    } else if (error.message === '已领取') {
      showToast('奖品已发放，请到锁权记录查看');
    } else {
      showToast('领取失败，请联系客服');
    }
  }
};

interface Sku {
  jdPrice: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

const skuList = ref<Sku[]>([]);

const getSkuList = async () => {
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const res = await httpRequest.post('/92020/orderSkuListPage', { pageNum: 1, pageSize: 100 });
    skuList.value = res.data.records ?? [];
    skuList.value.forEach((item) => {
      item.jdPrice = item.jdPrice?.split('.')[0];
    });
    closeToast();
  } catch (error: any) {
    closeToast();
    if (error.message) {
      showToast(error.message);
    }
  }
};

getSkuList();

const init = () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
};
init();
</script>

<style>
/* @font-face {
  font-family: 'FZLTKHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTKHJW/FZLTKHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTKHJW/FZLTKHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'CenturyGothic';
  src: url('./assets/CenturyGothic.woff2') format('woff2'), url('./assets/CenturyGothic.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'FZLTHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'CenturyGothic', 'FZLTKHJW';
} */
</style>

<style scoped lang='scss'>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-top: 11.1rem;
  position: relative;
}

.header-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 0.54rem 0 0.3rem 0.3rem;
  display: flex;
  justify-content: space-between;
}

.shop-name {
  font-size: 0.24rem;
}

.tip-type {
  text-align: center;
  font-size: 0.48rem;
  margin: 0.15rem auto 0.35rem auto;
  color: #2c3e50;
}

.exchange-area {
  margin: 0 auto;
  width: 100%;

  .btn {
    width: 3.44rem;
    height: 0.66rem;
    border-radius: 0.33rem;
    color: white;
    background: #cb0304;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    font-size: 0.38rem;
  }

  .tip {
    text-align: center;
    font-size: 0.14rem;
    color: #000;
  }
}

.btn-list {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 0.4rem;
  margin-top: 0.4rem;

  div {
    font-size: 0.36rem;
    color: #2c3e50;
  }
}

.segmentation {
  width: 100%;
  margin-bottom: 0.3rem;
}

.step-img {
  width: 100%;
}

.suspend-view {
  width: 1.5rem;
  height: 1.5rem;
  position: fixed;
  right: .1rem;
  top: 11rem;
  z-index: 6;

  img {
    width: 100%;
  }
}

.sku-view {
  width: 7.5rem;
  height: 10.6rem;
  position: relative;
  padding-top: 1.3rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/296525/2/24154/114997/68a6da0aF1485c634/336c63ecc08826c2.png");
    size: 100% 100%;
    repeat: no-repeat;
  };

  .sku-list {
    width: 7.5rem;
    height: 8.5rem;
    overflow-y: auto;
    position: relative;
    padding: 0 .15rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;

    .sku-item {
      width: 3.6rem;
      height: 4.15rem;
      position: relative;
      padding: .2rem .4rem 0 .2rem;
      background: {
        image: url("//img10.360buyimg.com/imgzone/jfs/t1/328312/35/8074/6346/68a59639Fb043414c/b6a295ed7c869b2d.png");
        size: contain;
        repeat: no-repeat;
      };

      .sku-img-view {
        width: 2.4rem;
        height: 2.2rem;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .sku-img {
          width: 2rem;
        }
      }

      .sku-name {
        font-size: .21rem;
        text-align: left;
        color: #000000;
        margin-top: .25rem;
      }

      .sku-btn {
        width: 3.3rem;
        height: .58rem;
        line-height: .6rem;
        padding: 0 .05rem 0 1rem;
        position: absolute;
        bottom: .2rem;
        left: 50%;
        transform: translateX(-53%);
        display: flex;
        justify-content: space-between;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/293255/23/17779/6638/68a59637F304f2f2a/a76af33d82219ae0.png");
          repeat: no-repeat;
          size: contain;
        };

        .price {
          font-weight: bold;
          font-size: .36rem;
          width: 1rem;
          text-align: left;
        }

        .buy {
          width: 1.2rem;
          height: .5rem;
          margin-top: .03rem;
          background-color: #ce0202;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: .25rem;
        }
      }
    }
  }
}


.two-line-omit {
  white-space: pre-wrap;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
