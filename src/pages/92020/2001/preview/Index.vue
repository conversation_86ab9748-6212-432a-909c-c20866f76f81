<template>
  <div class='bg' :style='furnishStyles.pageBg.value' :class='{ select: showSelect }' v-if='isLoadingFinish'>
    <div class='header-content'>
      <div class='shop-name' :style='furnishStyles.shopNameColor.value'>
        <span v-if='furnish.disableShopName === 1'>{{ shopName }}</span>
      </div>
    </div>
    <div class='exchange-area select-hover' :style='furnishStyles.giftImg.value' :class="{ 'on-select': selectedId === 2 }" @click='onSelected(2)'>
      <div class='btn' @click='toast'>立即参与 ></div>
      <div class='btn-list'>
        <div :style='furnishStyles.headerBtn.value' @click='rulePopup = true'>活动规则</div>
        <div>&nbsp;|&nbsp;</div>
        <div :style='furnishStyles.headerBtn.value' @click='recordPopup = true'>锁权记录</div>
      </div>
    </div>

    <div class='suspend-view' v-if='furnish.isShowSuspend'>
      <img :src='furnish?.suspendImg' @click='toast' alt=''>
    </div>

    <div class='sku-view' :style='{backgroundImage:`url(${furnish.skuModuleBg})`}' v-if='skuList?.length>0'>
      <div class='sku-list' :style="{height:skuList?.length<=2?'4.1rem':'8.5rem'}">
        <div class='sku-item' :style='{backgroundImage:`url(${furnish.skuItemBg})`}' v-for='item in skuList' :key='item.skuId'>
          <div class='sku-img-view'>
            <img :src='item.skuMainPicture' alt='' class='sku-img' />
          </div>
          <div class='sku-name two-line-omit'>{{ item.skuName }}</div>
          <div class='sku-btn'>
            <div class='price'><span style='font-size: .18rem'>￥</span>{{ item.jdPrice?.split('.')[0] }}</div>
            <div class='buy' @click='toast' :style='{backgroundColor:furnish.skuBtnColor}'>
              <img style='width: .35rem' src='//img10.360buyimg.com/imgzone/jfs/t1/327187/8/8601/655/68a6d7b9Ff2cdbfa3/b3ee0e53d72ef030.png' alt=''>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <van-popup v-model:show='rulePopup' :closeOnClickOverlay='false'>
    <PopupBk @close='rulePopup = false'>
      <Rule :ruleText='ruleText'></Rule>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='recordPopup' :closeOnClickOverlay='false'>
    <PopupBk v-if='recordPopup' @close='recordPopup = false'>
      <Record :tab='1'></Record>
    </PopupBk>
  </van-popup>
  <van-popup v-model:show='failPopup' :closeOnClickOverlay='false'>
    <PopupBk @close='failPopup = false'>
      <Fail></Fail>
    </PopupBk>
  </van-popup>
</template>

<script lang='ts' setup>
import { inject, onMounted, ref } from 'vue';
import furnishStyles, { furnish } from '../ts/furnishStyles';
import useSendMessage from '@/hooks/useSendMessage';
import useHtmlToCanvas from '@/hooks/useHtmlToCanvas';
import usePostMessage from '@/hooks/usePostMessage';
import { showToast } from 'vant';
import PopupBk from '../components/PopupBk.vue';
import Rule from '../components/Rule.vue';
import Record from '../components/Record.vue';
import Fail from '../components/Fail.vue';
import dayjs from 'dayjs';

const { registerHandler } = usePostMessage();

const activityData = inject('activityData') as any;
const decoData = inject('decoData') as any;

const shopName = ref('xxx自营旗舰店');

const isLoadingFinish = ref(false);

const rulePopup = ref(false);
const ruleText = ref('');

const recordPopup = ref(false);
const failPopup = ref(false);

const skuList = ref([]);
const powerRangeDate = ref([]);
const orderDataList = ref<any[]>([]);

// 装修时选择框
const showSelect = ref(false);
const selectedId = ref(1); // 装修时选择框序号
// 改变选择框
const onSelected = (id: number) => {
  useSendMessage('deco', 'changeSelect', id);
  selectedId.value = id;
};

const createImg = async () => {
  rulePopup.value = false;
  recordPopup.value = false;
  useHtmlToCanvas(document.getElementById('interact-c')!);
};

// 装修数据监听
registerHandler('deco', (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler('activity', (data) => {
  ruleText.value = data.rules;
  shopName.value = data.shopName;
  skuList.value = data.skuList;
  powerRangeDate.value = data.powerRangeDate;
  orderDataList.value = data.orderDataList;
});
// 店铺信息监听
registerHandler('shop', (data: string) => {
  shopName.value = data;
});
// 截图监听
registerHandler('screen', (data: any) => {
  createImg();
});
// 边框监听
registerHandler('border', (data: any) => {
  showSelect.value = data;
});
// 弹窗监听
registerHandler('popup', (data: any) => {
  failPopup.value = data;
});

onMounted(() => {
  useSendMessage('mounted', 'sendMounted', true);
  if (activityData) {
    document.title = activityData?.activityName;
    shopName.value = activityData.shopName;
    ruleText.value = activityData.rules;
    skuList.value = activityData.skuList;
    console.log(activityData, 9999);
    powerRangeDate.value = activityData.powerRangeDate;
    orderDataList.value = activityData.orderDataList;
    showToast('活动预览，仅供查看');
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
});

const toast = () => {
  showToast('活动预览，仅供查看');
};
</script>

<style>
/* @font-face {
  font-family: 'FZLTKHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTKHJW/FZLTKHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTKHJW/FZLTKHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'CenturyGothic';
  src: url('../assets/CenturyGothic.woff2') format('woff2'), url('../assets/CenturyGothic.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'FZLTHJW';
  src: url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff2') format('woff2'), url('https://lzcdn.dianpusoft.cn/fonts/FZLTHJW/FZLTHJW--GB1-0.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
* {
  font-family: 'CenturyGothic', 'FZLTKHJW';
} */
</style>
<style scoped lang='scss'>
.bg {
  background-size: contain;
  min-height: 100vh;
  background-repeat: no-repeat;
  padding-top: 11.1rem;
  position: relative;
  overflow-x: hidden;
}


.header-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 0.54rem 0 0.3rem 0.3rem;
  display: flex;
  justify-content: space-between;
}

.shop-name {
  font-size: 0.24rem;
}

.tip-type {
  text-align: center;
  font-size: 0.48rem;
  margin: 0.15rem auto 0.35rem auto;
  color: #2c3e50;
}

.exchange-area {
  margin: 0 auto;
  width: 100%;

  .btn {
    width: 3.44rem;
    height: 0.66rem;
    border-radius: 0.33rem;
    color: white;
    background: #cb0304;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    font-size: 0.38rem;
  }

  .tip {
    text-align: center;
    font-size: 0.14rem;
    color: #000;
  }
}

.btn-list {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 0.4rem;
  margin-top: 0.4rem;

  div {
    font-size: 0.36rem;
    color: #2c3e50;
    cursor: pointer;
  }
}

.segmentation {
  width: 100%;
  margin-bottom: 0.3rem;
}

.step-img {
  width: 100%;
}

.suspend-view {
  width: 1.5rem;
  height: 1.5rem;
  position: fixed;
  right: .1rem;
  top: 11rem;
  z-index: 6;

  img {
    width: 100%;
  }
}


.sku-view {
  width: 7.5rem;
  height: 10.6rem;
  position: relative;
  padding-top: 1.3rem;
  background: {
    image: url("//img10.360buyimg.com/imgzone/jfs/t1/296525/2/24154/114997/68a6da0aF1485c634/336c63ecc08826c2.png");
    size: 100% 100%;
    repeat: no-repeat;
  };

  .sku-list {
    overflow-y: auto;
    position: relative;
    padding: 0 .15rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;

    .sku-item {
      width: 3.5rem;
      height: 4.15rem;
      position: relative;
      padding: .2rem .4rem 0 .2rem;
      background: {
        image: url("//img10.360buyimg.com/imgzone/jfs/t1/328312/35/8074/6346/68a59639Fb043414c/b6a295ed7c869b2d.png");
        size: contain;
        repeat: no-repeat;
      };

      .sku-img-view {
        width: 2.4rem;
        height: 2.2rem;
        margin: 0 auto;
        display: flex;
        align-items: center;
        justify-content: center;

        .sku-img {
          width: 2rem;
        }
      }

      .sku-name {
        font-size: .21rem;
        text-align: left;
        color: #000000;
        margin-top: .25rem;
      }

      .sku-btn {
        width: 3.3rem;
        height: .58rem;
        line-height: .6rem;
        padding: 0 .05rem 0 1rem;
        position: absolute;
        bottom: .2rem;
        left: 50%;
        transform: translateX(-53%);
        display: flex;
        justify-content: space-between;
        background: {
          image: url("//img10.360buyimg.com/imgzone/jfs/t1/293255/23/17779/6638/68a59637F304f2f2a/a76af33d82219ae0.png");
          repeat: no-repeat;
          size: contain;
        };

        .price {
          font-weight: bold;
          font-size: .36rem;
          width: 1rem;
          text-align: left;
        }

        .buy {
          width: 1.2rem;
          height: .5rem;
          margin-top: .03rem;
          background-color: #ce0202;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: .25rem;
        }
      }
    }
  }
}


.two-line-omit {
  white-space: pre-wrap;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
</style>
