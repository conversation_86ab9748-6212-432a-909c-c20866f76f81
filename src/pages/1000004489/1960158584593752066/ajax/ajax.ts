import { showLoadingToast, showToast, closeToast } from 'vant';
import { httpRequest } from '@/utils/service';

// 活动主接口
export const getCategoryList = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getCategory');
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 根据所选类目查询商品
export const getSkuInfoList = async (params: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getSkuInfos', params);
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 根据所选商品查询对比参数、规格信息
export const getSkuDetailList = async (params: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getSkuDetails', params);
    // closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 查询对比参数
export const getSkuDetailParameterList = async (categoryId: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getParameters', { categoryId });
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 热门推荐商品列表
export const getSkuDetailHotList = async (categoryId: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getHotSkuInfos', { categoryId });
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 店铺金榜商品列表
export const getGoldenSkuInfos = async (categoryId: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getGoldenSkuInfos', { categoryId });
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 购物车商品列表
export const getSkuDetailCartList = async (categoryId: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getCartSkuInfos', { categoryId });
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 查询规格信息
export const getSpecifications = async (params: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/getSpecifications', params);
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
// 获取使用指南（存在gms 活动规则里）
export const getActivityRules = async () => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.get('/common/getRule');
    closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
//
export const compareEvent = async (params: any) => {
  try {
    // showLoadingToast({
    //   duration: 0,
    //   forbidClick: true,
    //   message: '倒计时 3 秒',
    // });
    const { data, code, msg } = await httpRequest.post('/haier/model/compare/compareEvent', params);
    // closeToast();
    if (code !== 200) {
      showToast(msg);
      return msg;
    }
    return data;
  } catch (error) {
    if (error && error.message) {
      showToast(error.message);
    }
  }
  return '';
};
