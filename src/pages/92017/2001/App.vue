<template>
  <div class="bg" :style="{ backgroundImage: `url(${decoData.pageBg})`, backgroundColor: decoData.actBgColor }">
    <!--     活动未开启点击遮罩层 -->
    <!--    <div class='no-start-mask' @click.stop="showToast('活动未开始')" v-if='baseInfo?.thresholdResponseList[0]?.thresholdCode===1'></div>-->

    <div class="header-btn-box">
      <div class="header-btn" v-for="(btn, index) in btnList" :key="index" @click="btn.event"></div>
    </div>

    <div class="checkIn-tip-view" v-html="getActTip()"></div>

    <div class="checkIn-level-view">
      <div class="process-line"></div>

      <div ref="swiperRef" class="swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide" v-for="(item, index) in activityInfo.assetsList" :key="index">
            <div class="prize-view">
              <div class="prize-image-view">
                <div class="prize-tip">
                  打卡<span style="font-size: 0.25rem">{{ item.sceneThreshold }}</span
                  >个场景
                </div>
                <img class="prize-image" :src="item.thresholdImg" alt="" />
              </div>
              <img class="point" src="//img10.360buyimg.com/imgzone/jfs/t1/317534/9/18319/286/68804d2fF44196f9c/fd2572dbb635e25f.png" alt="" />
              <img class="prize-draw-btn" @click="choosePrize(item)" :src="getAssetsStatus(item.sceneStatus)" alt="" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="scene-view">
      <div class="scene-title">{{ decoData.sceneTitle ?? '场景购物攻略' }}</div>
      <div class="scene-title-tip">{{ decoData.sceneSecondTitle ?? '购买专场内商品，满额解锁奖品，买多领多' }}</div>

      <div class="scene-list">
        <div class="scene-item" v-for="(item, index) in activityInfo.sceneList" :key="index" @click="goLinkHref(item.sceneLink)">
          <img class="achieved" v-if="item.sceneStatus === 2" src="//img10.360buyimg.com/imgzone/jfs/t1/324523/30/6603/1554/68a2aee9Fc0b1a5ec/a2a918e2f7192e0d.png" alt="" />
          <div class="scene-bg" :style="{ backgroundImage: `url(${getSceneBg(item.sceneStatus)})` }">
            <img class="scene-img" :src="item.sceneImg" alt="" />
          </div>
          <div class="scene-price" v-html="getSceneTip(item)"></div>
          <div class="scene-btn">{{ item.sceneName }}</div>
        </div>
      </div>
    </div>

    <div class="special-user-tip" v-if="isSpecialUser" style="position: absolute; top: 0; left: 0; z-index: 9999">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/331355/20/8673/190307/68b81096F4005db45/02302f380a1bc04e.png" alt="" style="width: 7.5rem" />
    </div>

    <VanPopup v-model:show="popupShow" :close-on-click-overlay="false">
      <!-- 规则弹窗 -->
      <RulePopup :rule="activityInfo.rule" v-if="popupName === 'rulePopup'"></RulePopup>
      <!-- 我的奖品弹窗 -->
      <MyPrize v-if="popupName === 'myPrizePopup'" :activityInfo="activityInfo" @fillAddress="fillAddress"></MyPrize>
      <MyOrder v-if="popupName === 'myOrderPopup'" :activityInfo="activityInfo"></MyOrder>
      <!-- 领奖成功弹窗 发放到账 -->
      <SuccessTipPopup v-if="popupName === 'successTipPopup'" :drawSuccessPrize="drawSuccessPrize" :exchangeType="currentChooseAssets.exchangeType"></SuccessTipPopup>
      <SuccessCardKeyPopup v-if="popupName === 'successCardKeyPopup'" :drawSuccessPrize="drawSuccessPrize"></SuccessCardKeyPopup>
      <!-- 领奖失败弹窗 -->
      <DrawFailPopup v-if="popupName === 'drawFailPopup'" :popupImg="decoData.drawFailPopup" :jumpUrl="decoData.drawFailBtnUrl"></DrawFailPopup>
      <!-- 非会员弹窗 -->
      <NoMemberPopup v-if="popupName === 'noMemberPopup'"></NoMemberPopup>
      <!-- 填写地址弹窗 -->
      <AddressPopup v-if="popupName === 'addressPopup'" :fillAddressPrize="drawSuccessPrize" @receiveGift="receiveGift"></AddressPopup>
      <!-- 填写地址成功弹窗 -->
      <AddressSuccessPopup v-if="popupName === 'addressSuccessPopup'"></AddressSuccessPopup>
      <ChoosePrizePopup v-if="popupName === 'choosePrizePopup'" :currentChooseAssets="currentChooseAssets" @drawPrize="drawPrize"></ChoosePrizePopup>
      <FillEquityPopup v-if="popupName === 'fillEquityPopup'" :drawSuccessPrize="drawSuccessPrize"></FillEquityPopup>
      <SignSuccessPopup v-if="popupName === 'signSuccessPopup'" :showPrize="showPrize" @checkMemberStatus="checkThreshold"></SignSuccessPopup>
    </VanPopup>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, reactive, onMounted, nextTick } from 'vue';
import furnishStyles, { furnish } from './ts/furnishStyles';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { IActivityInfo, IAssets, IPrize, IScene } from './ts/type';
import { httpRequest } from '@/utils/service';
import { BaseInfo } from '@/types/BaseInfo';
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';

import { popupShow, popupName, openPopup, closePopup } from './ts/popup';
import RulePopup from './components/RulePopup.vue';
import MyPrize from './components/MyPrize.vue';
import MyOrder from './components/MyOrder.vue';
import AddressPopup from './components/AddressPopup.vue';
import DrawFailPopup from './components/DrawFailPopup.vue';
import NoMemberPopup from './components/NoMemberPopup.vue';
import SuccessTipPopup from './components/SuccessTipPopup.vue';
import SuccessCardKeyPopup from './components/SuccessCardKeyPopup.vue';
import AddressSuccessPopup from './components/AddressSuccessPopup.vue';
import ChoosePrizePopup from './components/ChoosePrizePopup.vue';
import FillEquityPopup from './components/FillEquityPopup.vue';
import SignSuccessPopup from './components/SignSuccessPopup.vue';

import Swiper from 'swiper';
import 'swiper/swiper.min.css';
import constant from '@/utils/constant';

const baseInfo = inject('baseInfo') as BaseInfo;
const decoData = inject('decoData') as any;
const pathParams = inject('pathParams') as any;
const activityInfo = reactive({}) as IActivityInfo; // 活动相关数据

const userPrizeId = ref('');
const skuList = ref([]);
const currentPage = ref(1);
const isLoading = ref(false);
const isEnd = ref(false);

const showPrize = ref({});
const currentDrawPrize = ref({});
const drawSuccessPrize = ref({});
const exchangePrizeList = ref([]);

const swiperRef = ref(null);
const swiperObj = ref();
const initSwiper = () => {
  swiperObj.value = new Swiper('.swiper-container', {
    slidesPerView: 'auto', // 自适应展示
    centeredSlides: true, // 居中显示
    spaceBetween: 20, // 间距
    on: {
      slideChange() {
        // emits('slideChange', this.activeIndex);
      },
      click() {
        swiperObj.value.slideTo(this.clickedIndex);
      },
    },
  });
};

const getActTip = () => {
  if (activityInfo.againSceneCount <= 0) {
    return `你已打卡成功 <span style='font-size: .4rem;font-weight: bold'>${activityInfo.sceneCount ?? 0}</span> 个专场，<br />可领取全部奖励`;
  }
  return `你已打卡成功 <span style='font-size: .4rem;font-weight: bold'>${activityInfo.sceneCount ?? 0}</span> 个专场，<br />继续再打卡 <span style='font-size: .4rem;font-weight: bold'>${activityInfo.againSceneCount}</span> 个即可领取奖励`;
};

const getSceneTip = (scene: any) => {
  if (scene.hasScenePrice === scene.scenePrice) {
    return '已满足该专场金额';
  } else if (scene.hasScenePrice) {
    return `<span style='font-weight: bold;color: #000000'>已买${scene.hasScenePrice ?? 0}元<span>/<span style='color: #999999'>${scene.scenePrice ?? 0}元<span>`;
  } else {
    return '未购买本专场商品';
  }
};

const checkThreshold = () => {
  if (baseInfo.thresholdResponseList.length) {
    const list = baseInfo?.thresholdResponseList;
    if (list?.findIndex((i: any) => i.thresholdCode === 1) > -1) {
      showToast('活动未开始');
      return false;
    }
    if (list?.findIndex((i: any) => i.thresholdCode === 2) > -1) {
      showToast('活动已结束');
      return false;
    }
    if (list?.findIndex((i: any) => i.thresholdCode === 3) > -1) {
      showToast('您不是此活动参与的指定人群');
      return false;
    }
    if (list?.findIndex((i: any) => i.thresholdCode === 4) > -1) {
      // 非会员
      openPopup('noMemberPopup');
      return false;
    }
    if (list?.findIndex((i: any) => i.thresholdCode === 7) > -1) {
      showToast('会员等级不满足');
      return false;
    }
    if (list?.findIndex((i: any) => i.thresholdCode === 10) > -1) {
      showToast('入会时间不满足');
      return false;
    }
  }
  return true;
};

const goLinkHref = (link: string) => {
  if (!checkThreshold()) return;
  if (link) {
    window.location.href = link;
  }
};

const getSceneBg = (status: number) => {
  const l = [decoData.sceneBg_0, decoData.sceneBg_1, decoData.sceneBg_2];
  return l[status];
};

const getAssetsStatus = (status: number) => {
  switch (status) {
    case 1:
      // 未解锁
      return '//img10.360buyimg.com/imgzone/jfs/t1/333241/24/670/3090/68a4468eF2d252599/46d048cd58e404b4.png';
    case 2:
      // 立即领取
      return '//img10.360buyimg.com/imgzone/jfs/t1/327783/13/7458/3558/68a4468eFf30674de/e4b4e89a1d9d5c5c.png';
    case 3:
      // 已领取
      return '//img10.360buyimg.com/imgzone/jfs/t1/332283/19/631/3365/68a4468eF1a9ef977/4b60c14c13a44d49.png';
  }
};

// const getActStatus = () => {
//   if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 1) {
//     showToast('活动未开始');
//     return false;
//   }
//   if (baseInfo?.thresholdResponseList[0]?.thresholdCode === 2) {
//     showToast('活动已结束');
//     return false;
//   }
//   return true;
// };

// 获取活动规则
const getRule = async () => {
  try {
    const { data } = await httpRequest.get('/common/getRule');
    Object.assign(activityInfo, { rule: data });
    openPopup('rulePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取奖品
const getPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92017/getPrizes');
    Object.assign(activityInfo, { assetsList: data });
    const list = baseInfo?.thresholdResponseList;
    const actNoStart = list?.findIndex((i: any) => i.thresholdCode === 1) === -1;
    const actEnd = list?.findIndex((i: any) => i.thresholdCode === 2) === -1;

    if (actNoStart && actEnd) {
      showPrize.value = data[0];
      await getUserStatus();
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取用户是否首次进入活动
const getUserStatus = async () => {
  try {
    const { data } = await httpRequest.post('/92017/pin');
    if (!data) {
      openPopup('signSuccessPopup');
    }
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取活动信息
const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/92017/activity');
    Object.assign(activityInfo, { ...data });
    await getPrizes();
    await nextTick(() => {
      initSwiper();
    });
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取用户奖品
const getUserPrizes = async () => {
  try {
    const { data } = await httpRequest.post('/92017/getUserPrizes');
    Object.assign(activityInfo, { userPrizeList: data });
    openPopup('myPrizePopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

// 获取用户订单
const getUserOrders = async () => {
  try {
    const { data } = await httpRequest.post('/92017/getUserOrders');
    Object.assign(activityInfo, { userOrderList: data });
    openPopup('myOrderPopup');
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

const currentChooseAssets = ref({});
const currentChoosePrize = ref([]);
const receiveGift = async (addressInfo?: any) => {
  // if (!checkThreshold()) return;
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    // const data = {
    //   'activityPrizeId': '1948631740350631937',
    //   'couponSkuId': null,
    //   'exchangeImg': null,
    //   'failReason': null,
    //   'prizeImg': '//img10.360buyimg.com/imgzone/jfs/t1/199905/32/493/7224/61039d4fE6c987585/02de76ea362183d6.png',
    //   'prizeName': '测试-sh020',
    //   'prizeType': 12,
    //   'result': {
    //     'cardDesc': '12121221',
    //     'cardNumber': '100124546',
    //     'cardPassword': null,
    //     'id': 2,
    //   },
    //   'sortId': 1,
    //   'status': 1,
    //   'userPrizeId': '1948631999281414146',
    // };
    const { data } = await httpRequest.post('/92017/receivePrize', { exchangeType: currentChooseAssets.value.exchangeType, prizeIds: currentChoosePrize.value.map((i: any) => i.prizeId), ...addressInfo });
    closeToast();
    if (data.status === 2) {
      // 如果领取失败
      showToast('领取失败，请稍后再试');
      closePopup();
    }
    if (data.status === 1) {
      // 如果兑换成功
      drawSuccessPrize.value = data;
      if (currentChooseAssets.value.exchangeType === 1) {
        openPopup('successTipPopup');
      } else {
        if (data.prizeType === 7) {
          // 卡密
          openPopup('successCardKeyPopup');
        } else if (data.prizeType === 3) {
          // 实物
          openPopup('addressSuccessPopup');
        } else if (data.prizeType === 12) {
          // 福禄权益领取
          openPopup('fillEquityPopup');
        } else {
          // 直发到账
          openPopup('successTipPopup');
        }
      }
    }
    await getActivityInfo();
  } catch (error: any) {
    showToast(error.message);
    console.error(error);
  }
};

const choosePrize = (asset: IAssets) => {
  if (!checkThreshold()) return;
  if (asset.sceneStatus === 2) {
    currentChooseAssets.value = asset as any;
    openPopup('choosePrizePopup');
  }
};

const drawPrize = (choosePrizeList: any) => {
  if (!checkThreshold()) return;
  console.log('选择的奖品:', choosePrizeList);
  // 判断奖品是否存在实物
  currentChoosePrize.value = choosePrizeList;
  if (currentChooseAssets.value.exchangeType === 1) {
    // 如果多个奖品领取 直接领取后去我的奖品里填写信息
    receiveGift();
  } else {
    // 如果单个奖品领取
    const isShiWu = choosePrizeList.findIndex((i: any) => i.prizeType === 3) > -1;
    if (isShiWu) {
      // 实物奖品 弹窗填写地址
      openPopup('addressPopup');
    } else {
      // 其他奖品 直接领取
      receiveGift();
    }
  }
};

const fillAddress = (info: any) => {
  if (!checkThreshold()) return;
  drawSuccessPrize.value = info;
  if (info.prizeType === '3') {
    openPopup('addressPopup');
  } else if (info.prizeType === '7') {
    drawSuccessPrize.value.result = JSON.parse(info.prizeContent);
    openPopup('successCardKeyPopup');
  } else if (info.prizeType === '12') {
    drawSuccessPrize.value = info;
    openPopup('fillEquityPopup');
  }
};

const btnList: {
  name: string;
  event?: () => void;
}[] = [
  {
    name: '我的奖品',
    event: () => {
      getUserPrizes();
    },
  },
  {
    name: '您的订单',
    event: () => {
      getUserOrders();
    },
  },
  {
    name: '活动规则',
    event: () => {
      getRule();
    },
  },
];

onMounted(() => {
  getActivityInfo();
});

const isSpecialUser = ref(false);

if (baseInfo.activityMainId === '1957782926163935233') {
  const pinList = ['xEzQgAbQ0H/8dNSUvruJPorwo1KjqfhhTE66jKXD8r12uMveYQ+rqXnjbq1mOVLU', 'xeNRiaepzDHk4S6ZN20SACuuadQPA0cYqp9ZkvcOQeE9ilbo+RyNDutiw5Zsqz/pkdK3rLBQpEQH9V4tdrrh0w==', '1mYZ7kDxEwoHJWB0pAPbNCuuadQPA0cYqp9ZkvcOQeE9ilbo+RyNDutiw5Zsqz/pkdK3rLBQpEQH9V4tdrrh0w=='];
  const userPin = window.sessionStorage.getItem(constant.LZ_JD_ENCRYPT_PIN) || '';
  if (pinList.includes(userPin)) {
    isSpecialUser.value = true;
  }
}
</script>

<style scoped lang="scss"></style>
<style></style>
