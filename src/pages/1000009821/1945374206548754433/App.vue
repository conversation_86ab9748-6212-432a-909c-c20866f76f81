<!--
 * SKII入会小样赠礼定制开发
 * @author: wuhao
 * @since: 2025-01-21
 * App.vue
-->
<template>
  <div class="container" :class="{'container2': actData.orderStatus !== 4}">
    <div class="rule-btn" @click="getRule1"></div>
    <div class="bottom-box" v-if="actData.orderStatus === 4">
      <div style="flex: 1;" @click="openOtherAct"></div>
      <div style="flex: 1;"></div>
    </div>
    <div class="bottom-box" v-else>
      <div style="flex: 1;" @click="openOtherAct"></div>
      <div style="flex: 1;" @click="showOrderPrize = true"></div>
    </div>
  </div>
  <div class="prizecontainer" :class="`prizecontainer-${actData.orderStatus}`" v-if="showOrderPrize">
    <div class="rule-btn" @click="getRule2"></div>
    <div class="bottom-box" @click="orderFn"></div>
    <div class="bottom-box" @click="getPrize" v-if="actData.orderStatus === 2"></div>
  </div>
  <RulePopup :showPopup="showPopup.showRule" :rule="rule" @closePopup="showPopup.showRule = false"></RulePopup>
  <MyPrizePopup :showPopup="showPopup.showMyPrize" :prize="record" @closePopup="showPopup.showMyPrize = false"></MyPrizePopup>
  <ConfirmPopup :showPopup="showPopup.showConfirmPopup" @closePopup="showPopup.showConfirmPopup = false" @getRights="getRights"></ConfirmPopup>
</template>

<script setup lang="ts">
import { inject, onMounted, reactive, ref } from 'vue';
import { showLoadingToast, showToast, closeToast } from 'vant';
import { getUserStatus, receiveRights, getRightsRecord, getRule } from './ajax/ajax';
import { BaseInfo } from '@/types/BaseInfo';
import CLIENT_TYPE, { getClientType } from '@/utils/platforms/clientType';
import { gotoSkuPage, gotoShopPage } from '@/utils/platforms/jump';
import constant from '@/utils/constant';
import RulePopup from './compoents/RulePopup.vue';
import MyPrizePopup from './compoents/MyPrizePopup.vue';
import ConfirmPopup from './compoents/ConfirmPopup.vue';

const baseInfo = inject('baseInfo') as BaseInfo;
const text = ref('');
const showOrderPrize = ref(false);
const rule = ref('');
const actData = ref<any>({
  skuId: '',
  orderStatus: 4,// 下单活动状态：1:无库存；2：已领过；3：可领取；4:未开始；5:无订单；
  status: 4,// 领小样活动状态：1:无库存；2：已领过；3：可领取
});
const showPopup = reactive<any>({
  showRule: false,
  showMyPrize: false,
  showConfirmPopup: false,
});
const record = ref<any>({});

const openOtherAct = () => {
  const pathurl = window.location.href.replace('1945374206548754433', '1946034487725637633')
  
  window.location.href = pathurl;
}
const getRights = async () => {
  const result = await receiveRights({type: 2});
  if (result.code === 200) {
    if (result.data === 3) {
      actData.value.orderStatus = 0
    } else {
      actData.value.orderStatus = result.data;
    }
    showPopup.showConfirmPopup = false;
  } else {
    console.log(result);
    showPopup.showConfirmPopup = false;
    showToast(result.message);
  }
};

const getPrize = async () => {
  record.value = await getRightsRecord({type: 2});
  showPopup.showMyPrize = true;
};
const orderFn = async () => {
  if (actData.value.orderStatus === 3) {
    showPopup.showConfirmPopup = true;
  } else if (actData.value.orderStatus === 5) {
    console.log(actData.value.skuId);
    
    gotoSkuPage(actData.value.skuId);
  } else if (actData.value.orderStatus === 0) {
    gotoShopPage(baseInfo.shopId);
  }
   else {
    showOrderPrize.value = false;
  }
}
const getRule1 = async () => {
  rule.value = await getRule({type: 1});
  showPopup.showRule = true;
}
const getRule2 = async () => {
  rule.value = await getRule({type: 2});
  showPopup.showRule = true;
}
onMounted(async () => {

  actData.value = await getUserStatus(getClientType() === CLIENT_TYPE.JDAPP ? '01' : '02', sessionStorage.getItem(constant.LZ_JD_TOKEN));
if (!baseInfo.memberLevel) {
  const returnUrl = encodeURIComponent(`${window.location.href}&isJoin=1`);
  window.location.href = `https://shopmember.m.jd.com/shopcard?venderId=1000009821&shopId=1000009821&venderType=1&channel=8017026&returnUrl=${returnUrl}`;
}
  
});
</script>

<style scoped lang="scss">
.container {
  width: 7.5rem;
  height: 100vh;
  background-image: url('./assets/bg.png');
  background-repeat: no-repeat;
  background-size: contain;
  background-color: #830002;
  position: relative;
  padding-top: 11.6rem;
}
.rule-btn {
  width: 2.1rem;
  height: 0.5rem;
  position: absolute;
  right: 0;
  top: 0.45rem;
}
.bottom-box {
  width: 100%;
  height: 0.8rem;
  display: flex;
  justify-content: space-between;
}
.container2 {
  background-image: url('./assets/bg2.png');
}
.prizecontainer {
  width: 7.5rem;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: contain;
  background-color: #830002;
  position: fixed;
  top: 0;
  left: 0;
  padding-top: 11.4rem;
  box-sizing: border-box;
}
.prizecontainer-0 {
  background-image: url('./assets/0.jpg');
}
.prizecontainer-1 {
  background-image: url('./assets/1.jpg');
}
.prizecontainer-2 {
  background-image: url('./assets/2.jpg');
}
.prizecontainer-3 {
  background-image: url('./assets/3.png');
}
.prizecontainer-5 {
  background-image: url('./assets/5.jpg');
}
</style>
<style>
* {
  box-sizing: border-box;
}
</style>
