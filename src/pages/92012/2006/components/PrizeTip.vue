<template>
  <div class="rule-bk">
    <div class="title">
      <div class="leftLineDiv"></div>
      <div>奖品剩余活动提示</div>
      <div class="rightLineDiv"></div>
    </div>
    <div class="content">
      <div class="prize-list">
        <div class="prize" v-for="(item, index) in prizeList" :key="index">
          <div class="prize-img">
            <img :src="item.prizeImg" alt="" class="p-img" />
            <img v-if="item.remainNum" src="//img10.360buyimg.com/imgzone/jfs/t1/27063/8/16708/5073/628b0378Ea43f50a2/1c761eb2954b69a0.png" alt="" class="icon" />
            <img v-else src="//img10.360buyimg.com/imgzone/jfs/t1/116819/5/26595/3096/628b03c9Eeba5cece/7e54baacafade5bb.png" alt="" class="end" />
          </div>
          <div class="name" v-if="item.signType === 1">累计签到{{ item.signDays }}天</div>
          <div class="name" v-else-if="item.signType === 2">连续签到{{ item.signDays }}天</div>
          <div class="name" v-else>每日签到</div>
        </div>
      </div>
      <div class="tip">本期活动礼品有限！先到先得哦！（以签到成功顺序依次发放）唯有坚持，方得始终，预祝您参与成功~</div>
      <div>
        <VanCheckbox v-model="checked"><div class="check-box">我已阅读并知晓</div></VanCheckbox>
      </div>
      <div class="btn-bottom">
        <div class="btn" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
        <div class="btn btn-right" @click="close">知道了，继续签到</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { BaseInfo } from '@/types/BaseInfo';
import { showToast } from 'vant';
import { inject, ref } from 'vue';
import { gotoShopPage } from '@/utils/platforms/jump';

const baseInfo = inject('baseInfo') as BaseInfo;

const props = withDefaults(defineProps<{ prizeList: any[] }>(), {
  prizeList: () => [],
});

const emits = defineEmits(['close']);

const checked = ref(false);

const close = () => {
  if (checked.value) {
    emits('close');
  } else {
    showToast('请确认已阅读并知晓');
  }
};
</script>

<style scoped lang="scss">
.rule-bk {
  background-color: #f2f2f2;
  border-radius: 0.2rem 0.2rem 0 0;
  width: 100vw;

  .title {
    position: relative;
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/141588/29/18217/44025/5fd571b9Ef55329ec/42dba91b188b9dce.png);
    background-size: 100%;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.14rem;
    font-size: 0.34rem;
    color: #fff;

    .leftLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, left top, right top, from(#fff), to(#ff6153));
      background: linear-gradient(to right, #fff, #ff6153);
      border-radius: 4px;
      margin-right: 0.1rem;
    }

    .rightLineDiv {
      width: 0.68rem;
      height: 0.08rem;
      background: -webkit-gradient(linear, right top, left top, from(#fff), to(#ff8c4a));
      background: linear-gradient(to left, #fff, #ff8c4a);
      border-radius: 4px;
      margin-left: 0.1rem;
    }
  }

  .close {
    position: absolute;
    top: 0.26rem;
    right: 0.26rem;
    width: 0.22rem;
  }

  .content {
    // height: 5rem;
    border: 0.3rem solid transparent;
  }
}
.prize-list {
  display: flex;
  flex-wrap: wrap;
  .prize {
    width: 1.38rem;
    margin-bottom: 0.25rem;
    .prize-img {
      position: relative;
      width: 1rem;
      height: 1rem;
      margin: 0 auto;
      .p-img {
        width: 100%;
        height: 100%;
        border-radius: 0.16rem;
        background: linear-gradient(to bottom, #ffe888, #fdbc3c);
      }
      .icon {
        position: absolute;
        top: 0;
        left: 0;
        width: 0.6rem;
      }
      .end {
        position: absolute;
        right: -0.13rem;
        top: -0.1rem;
        width: 0.56rem;
      }
    }
    .name {
      text-align: center;
      font-size: 0.22rem;
      color: #343434;
      margin-top: 0.2rem;
    }
  }
}
.tip {
  font-size: 0.2rem;
  color: #999999;
  margin-bottom: 0.25rem;
}
.check-box {
  color: #323233;
  line-height: 20px;
  font-size: 0.18rem;
}
.btn-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.3rem;
  .btn {
    background: linear-gradient(to right, #f90, #f90);
    width: 3.34rem;
    padding: 0.28rem 0;
    border-radius: 0.16rem;
    text-align: center;
    font-size: 0.34rem;
    color: #fff;
  }
  .btn-right {
    background: linear-gradient(to right, #f2270c, #ff6420);
  }
}
</style>
