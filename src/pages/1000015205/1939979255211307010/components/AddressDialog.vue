<template>
  <van-popup class="my-dialog my-popup" :show="show" @click.stop>
    <div
      class="dialog-container animate__animated animate__faster"
      :class="{
        animate__bounceInUp: show,
        animate__zoomOut: !show,
      }">
      <div class="address-dialog">
        <div v-if="!!reShowData" class="dialog-close-btn" @click="$emit('update:show', false)"></div>
        <div class="address-form">
          <van-field v-model="addressFormData.realName" :readonly="isViewAddress" label="姓名：" placeholder="请输入姓名" class="address-form-field" :maxlength="10" />
          <van-field v-model="addressFormData.mobile" :readonly="isViewAddress" label="电话：" placeholder="收货人手机号" class="address-form-field" type="tel" :maxlength="11" />
          <van-field v-model="fullAddress" label="地区：" placeholder="选择省/市/区" class="address-form-field" readonly clickable right-icon="arrow-down" @click="addressInputClick" />
          <van-field v-model="addressFormData.address" :readonly="isViewAddress" label="详细地址：" placeholder="街道门牌号" class="address-form-field" />
        </div>
        <!-- 提交地址按钮 -->
        <div class="submit-address-button" v-if="!isViewAddress" @click="submit"></div>
      </div>
    </div>
    <!-- 选择省市区弹窗 -->
    <van-popup class="my-dialog my-popup" position="bottom" :show="showAreaDialog">
      <div class="choose-area-dialog">
        <van-area :area-list="areaList" :value="addressFormData.postalCode" @confirm="onAreaConfirm" @cancel="onAreaCancel" />
      </div>
    </van-popup>
  </van-popup>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import { areaList } from '@vant/area-data';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';

const props = defineProps<{
  show: boolean;
  grantId: any;
  isViewAddress?: boolean;
  reShowData?: any;
}>();

const emit = defineEmits<{
  (e: 'update:show', val: boolean): void;
  (e: 'submitSuccess'): void;
}>();

const addressFormData = ref({
  realName: '',
  mobile: '',
  province: '',
  city: '',
  county: '',
  address: '',
  postalCode: '',
});

watch(
  () => props.reShowData,
  (val) => {
    if (val) {
      addressFormData.value = {
        realName: val.realName || '',
        mobile: val.mobile || '',
        province: val.province || '',
        city: val.city || '',
        county: val.county || '',
        address: val.address || '',
        postalCode: val.postalCode || '',
      };
    }
  },
  { immediate: true },
);

const fullAddress = computed(() => {
  if (!addressFormData.value.province) return '';
  return `${addressFormData.value.province}/${addressFormData.value.city}/${addressFormData.value.county}`;
});

// 选择省市区弹窗
const showAreaDialog = ref(false);
// 点击地址输入框
const addressInputClick = () => {
  if (props.isViewAddress) return;
  showAreaDialog.value = true;
};
// 隐藏选择省市区弹窗
const onAreaCancel = () => {
  showAreaDialog.value = false;
};

const onAreaConfirm = ({ selectedOptions }) => {
  addressFormData.value.province = selectedOptions[0]?.text || '';
  addressFormData.value.city = selectedOptions[1]?.text || '';
  addressFormData.value.county = selectedOptions[2]?.text || '';
  addressFormData.value.postalCode = selectedOptions[2]?.value || '';
  showAreaDialog.value = false;
};

const submit = async () => {
  if (props.isViewAddress) return showToast('查看地址不能修改');
  console.log('提交地址', addressFormData.value);

  const iosFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  const androidFace = /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  const telValidator = (str: string) => {
    return /^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[01456879])|(?:5[0-3,5-9])|(?:6[2567])|(?:7[0-8])|(?:8[\d])|(?:9[0-3,5-9]))\d{8}$/.test(str);
  };
  const telValidator2 = (str: string) => {
    return /^1[3456789]\d{9}$/.test(str);
  };

  const { realName, mobile, province, city, county, address, postalCode } = addressFormData.value;

  if (!realName) {
    showToast('请填写收货人姓名');
    return;
  }

  if (realName.length > 10) {
    showToast('收货人姓名长度不得超过10个字符');
    return;
  }

  if (iosFace.test(realName) || androidFace.test(realName)) {
    showToast('收货人姓名不能含有表情');
    return;
  }

  if (!mobile) {
    showToast('请填写手机号');
    return;
  }

  if (!telValidator2(mobile) || !telValidator(mobile)) {
    showToast('请填写正确的手机号');
    return;
  }

  if (!province || !city || !county) {
    showToast('请选择省市区');
    return;
  }

  if (!address) {
    showToast('请填写详细地址');
    return;
  }

  if (address.length > 150) {
    showToast('收货地址长度超出限制');
    return;
  }

  if (iosFace.test(address) || androidFace.test(address)) {
    showToast('收货地址不能含有表情');
    return;
  }

  const submitData = {
    realName,
    mobile,
    province,
    city,
    county,
    address,
    postalCode,
    grantId: props.grantId,
  };

  try {
    showLoadingToast({ message: '正在提交...', forbidClick: true, duration: 0 });
    const res = await httpRequest.post('/99659/save/address', submitData);
    if (res.code === 200) {
      showToast({
        message: '提交成功',
        duration: 1000,
        forbidClick: true,
        onClose: () => {
          emit('update:show', false);
          emit('submitSuccess');
        },
      });
    }
  } catch (e: any) {
    showToast(e.message || '获取失败');
  } finally {
    closeToast();
  }
};
</script>

<style scoped lang="scss">
/* 填写地址弹窗 */
.address-dialog {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: (573 / (1 * 100)) * 1rem;
  height: (814 / (1 * 100)) * 1rem;
  padding-top: 1.8rem;
  background-image: url('../asset/dialog-address.png');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;

  :deep(.van-cell) {
    &::after {
      display: none;
    }
  }

  .address-form-field {
    margin-bottom: 0.4rem;
    padding: 0;
    font-size: 0.3rem;
    line-height: 1;
    background: transparent;

    :deep(.van-field__label) {
      display: flex;
      align-items: center;
      width: 1.4rem;
      margin-right: 0;
      color: #5d5d5d;
      white-space: nowrap;
      text-align: right;
      word-break: keep-all;

      label {
        width: 100%;
      }
    }

    :deep(.van-field__value) {
      display: flex;
      align-items: center;
      width: 3.3rem;
      height: 0.65rem;
      padding: 0 0.2rem;
      color: #5d5d5d;
      background-color: #fff;
      border: solid 0.01rem #f7ca97;
      border-radius: 0.15rem;

      .van-field__body {
        width: 100%;
      }
    }
  }
}

/* 提交地址按钮 */
.submit-address-button {
  position: absolute;
  bottom: 0.6rem;
  left: 50%;
  width: 2.8rem;
  height: 0.9rem;
  transform: translateX(-50%);
}

/* 选择省市区弹窗 */
.choose-area-dialog {
  width: 100vw;

  :deep(.van-picker) {
    width: 100%;
  }
}
</style>
