<template>
  <div class="com-bg">
    <div class="team-content">
      <div class="teammate">
        <div class="avatar">
          <img :src="groupInfo.sponsorAvatar" alt="" v-if="groupInfo.sponsorAvatar" />
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/329015/6/3839/3848/68abc551Fa250dc9b/32a5ecb161f09841.png" alt="" v-else @click="clickInvite" />
        </div>
        <div class="text">
          <div v-if="groupInfo.sponsorName">{{ groupInfo.sponsorName }}</div>
          <div v-if="groupInfo.sponsorName">{{ groupInfo.sponsorOrderComplete || teamStatus === 4 ? '已完成' : '未完成' }}</div>
        </div>
      </div>
      <div class="teammate">
        <div class="avatar">
          <img :src="groupInfo.helpAvatar" alt="" v-if="groupInfo.helpAvatar" />
          <img src="https://img10.360buyimg.com/imgzone/jfs/t1/329015/6/3839/3848/68abc551Fa250dc9b/32a5ecb161f09841.png" alt="" v-else @click="clickInvite" />
        </div>
        <div class="text">
          <div v-if="groupInfo.helpName">{{ groupInfo.helpName }}</div>
          <div v-if="groupInfo.helpName">{{ groupInfo.helpOrderComplete || teamStatus === 4 ? '已完成' : '未完成' }}</div>
        </div>
      </div>
    </div>
    <div class="btn">
      <img v-if="teamStatus === 0" src="https://img10.360buyimg.com/imgzone/jfs/t1/337173/32/1335/38751/68abc551F91c559a0/95e5db838b112ef5.png" alt="" @click="initiateGroupBuying" />
      <img v-else-if="teamStatus === 1" src="https://img10.360buyimg.com/imgzone/jfs/t1/325559/8/10438/38756/68abc550F6d17be20/153380bda1f6d540.png" alt="" @click="inviteFriend" />
      <img v-else-if="teamStatus === 2" src="https://img10.360buyimg.com/imgzone/jfs/t1/330103/36/3893/42536/68abc54fFd82bb35e/2100e61755d26eea.png" alt="" />
      <img v-else-if="teamStatus === 3" src="https://img10.360buyimg.com/imgzone/jfs/t1/338753/29/1336/39242/68abc54fFf14eff98/9a25db9af465989c.png" alt="" @click="receiveGroupAward" />
      <img v-else-if="teamStatus === 4" src="https://img10.360buyimg.com/imgzone/jfs/t1/331266/10/3833/41471/68abc54fF0fbe3726/892bf2fed81ec6c3.png" alt="" @click="initiateGroupBuying" />
    </div>
    <div class="record" @click="showSendRecordPop">{{ !!userGroupSendRecord.length ? `发放记录：${dayjs(userGroupSendRecord[0].finishTime).format('YYYY年M月D日 HH:mm:ss')}，已发放${userGroupSendRecord[0].sendChanceNum}次抽奖机会` : '发放记录：当前暂无发放记录' }}</div>
  </div>
  <VanPopup v-model:show="sendRecordPop" teleport="body" :close-on-click-overlay="false">
    <SendRecord :userGroupSendRecord="userGroupSendRecord" v-if="sendRecordPop" @close="sendRecordPop = false"></SendRecord>
  </VanPopup>
  <VanPopup v-model:show="joinTeamPop" teleport="body" :close-on-click-overlay="false">
    <JoinTeam :initiateInfo="initiateInfo" @close="joinTeamPop = false" @success="getGroupInfo"></JoinTeam>
  </VanPopup>
</template>

<script lang="ts" setup>
import { inject, reactive, ref } from 'vue';
import SendRecord from '../pop/SendRecord.vue';
import JoinTeam from '../pop/JoinTeam.vue';
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { callShare } from '@/utils/platforms/share';
import dayjs from 'dayjs';
import { checkActStatus, getActivityInfo } from '../hooks';

const pathParams = inject('pathParams') as any;

const sendRecordPop = ref(false);
const joinTeamPop = ref(false);

const showSendRecordPop = () => {
  checkActStatus();
  sendRecordPop.value = true;
};

const teamStatus = ref(0); // 0:组件团队 1：邀请好友 2：未完成订单 3：立即领奖 4：开启新团队

const userGroupSendRecord = ref<any[]>([]);
const getUserGroupSendRecord = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getUserGroupSendRecord');
    userGroupSendRecord.value = data;
  } catch (error) {}
};

const groupInfo = reactive({
  friendId: 0, // 拼团id
  sponsorAvatar: '', // 发起人头像
  sponsorName: '', // 发起人昵称
  sponsorOrderComplete: false, // 发起人是否完成订单
  helpAvatar: '', // 助力者头像
  helpName: '', // 助力者昵称
  helpOrderComplete: false, // 助力者是否完成订单
  canReceiveAward: false, // 是否可以领奖
  receiveAwardComplete: false, // 是否领奖完成
});
const getGroupInfo = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getGroupInfo');
    Object.assign(groupInfo, data);
    if (!groupInfo.friendId) {
      teamStatus.value = 0;
    } else if (!groupInfo.helpName) {
      teamStatus.value = 1;
    } else if (groupInfo.helpName && !groupInfo.canReceiveAward && !groupInfo.receiveAwardComplete) {
      teamStatus.value = 2;
    } else if (groupInfo.canReceiveAward && !groupInfo.receiveAwardComplete) {
      teamStatus.value = 3;
    } else if (groupInfo.receiveAwardComplete) {
      teamStatus.value = 4;
    }
    getUserGroupSendRecord();
  } catch (error) {}
};

const initiateGroupBuying = async () => {
  checkActStatus();
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/initiateGroupBuying');
    await getGroupInfo();
    closeToast();
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

// 邀请好友
const inviteFriend = () => {
  checkActStatus();
  const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
  callShare({
    title: shareConfig.shareTitle,
    content: shareConfig.shareContent,
    imageUrl: shareConfig.shareImage,
    shareUrl: `${process.env.VUE_APP_HOST}yili/midAutumnBuddyTest/?friendId=${groupInfo.friendId}`,
  });
};

// 领取奖励
const receiveGroupAward = async () => {
  checkActStatus();
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/receiveGroupAward', {
      friendId: groupInfo.friendId,
    });
    getActivityInfo();
    await getGroupInfo();
    closeToast();
    showToast('领取成功');
  } catch (error: any) {
    closeToast();
    showToast(error.message);
  }
};

const clickInvite = () => {
  if (teamStatus.value === 0) {
    initiateGroupBuying();
  } else if (teamStatus.value === 1) {
    inviteFriend();
  }
};

const initiateInfo = reactive({
  sponsorAvatar: '', // 发起人头像
  sponsorName: '', // 发起人昵称
  isSponsor: false, // 当前用户是否是发起人
});
// 获取要加入的拼团信息
const getInitiateInfo = async () => {
  try {
    checkActStatus();
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getInitiateInfo', {
      friendId: pathParams.friendId,
    });
    Object.assign(initiateInfo, data);
    if (!initiateInfo.isSponsor) {
      joinTeamPop.value = true;
    }
  } catch (error) {}
};
if (pathParams?.friendId) {
  getInitiateInfo();
}
getGroupInfo();
</script>

<style scoped lang="scss">
.com-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/328453/19/10626/243511/68abc54eF420d536a/2318f7c65250428f.png') no-repeat;
  background-size: 100%;
  height: 8.61rem;
  position: relative;
  padding-top: 3.61rem;
  margin-bottom: 0.13rem;
}
.team-content {
  display: flex;
  justify-content: center;
  margin-bottom: 0.15rem;
  .teammate {
    width: 2.5rem;
    .avatar {
      img {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        border: solid 0.067rem #ffc388;
        margin: 0 auto;
      }
    }
    .text {
      padding-top: 0.13rem;
      text-align: center;
      font-size: 0.18rem;
      font-weight: bold;
      line-height: 0.21rem;
      color: #fff;
      height: 0.55rem;
    }
  }
}
.btn {
  margin-bottom: 0.07rem;
  img {
    width: 5.56rem;
    margin-left: 1.03rem;
  }
}
.record {
  height: 0.37rem;
  text-align: center;
  font-size: 0.2rem;
  font-weight: bold;
  line-height: 0.37rem;
  color: #fff;
}
</style>
