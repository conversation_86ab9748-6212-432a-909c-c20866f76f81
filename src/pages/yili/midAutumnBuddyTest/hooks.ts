import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { showToast } from 'vant/es';
import { reactive, ref } from 'vue';

export const activityInfo = reactive({
  userRemainChance: 0, // 用户剩余抽奖次数
  freeOrderSendProgress: 0, // 每日免单发放进度
  addCartFinish: false, // 是否添加购物车完成
  newUser: false, // 是否新用户
});

export const getActivityInfo = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getActivityInfo');
    data.freeOrderSendProgress = Number(data.freeOrderSendProgress);
    data.userRemainChance = data.userRemainChance < 0 ? 0 : data.userRemainChance;
    Object.assign(activityInfo, data);
  } catch (error) {}
};

export const taskNum = ref(0);
export const groupOneFinishTask = ref(false);
export const groupTwoFinishTask = ref(false);

export const getOpenCardTask = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/openCardTask');
    groupOneFinishTask.value = data.groupOneFinishTask;
    groupTwoFinishTask.value = data.groupTwoFinishTask;
    if (data.groupOneFinishTask && data.groupTwoFinishTask) {
      taskNum.value = 2;
    } else if (data.groupOneFinishTask || data.groupTwoFinishTask) {
      taskNum.value = 1;
    } else {
      taskNum.value = 0;
    }
  } catch (error) {}
};

export const getOrderTask = async () => {
  try {
    await httpRequest.post('/brand/yiLiMidAutumn/orderTask');
  } catch (error) {}
};

export const winnerList = ref([]);

export const getAllPrizeRecord = async () => {
  try {
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getAllPrizeRecord');
    winnerList.value = data;
  } catch (error) {}
};

export const checkActStatus = (toast = true) => {
  const endTime = '2025-10-03 23:59:59';
  if (dayjs().isAfter(dayjs(endTime))) {
    toast && showToast('活动已结束');
    throw new Error('活动已结束');
  }
};
