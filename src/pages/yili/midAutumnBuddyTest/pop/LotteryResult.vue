<template>
  <div class="pop-bg">
    <div class="jingdou-bg" v-if="prizeInfo.prizeType === 2">
      <img :src="prizeInfo?.prizeImg" alt="" class="prize-img" />
      <svg class="prize-name" viewBox="0 0 500 80" xmlns="http://www.w3.org/2000/svg">
        <text x="250" y="20" class="prize-name-text">恭喜您成功抽中</text>
        <text x="250" y="60" class="prize-name-text">{{ prizeInfo?.prizeName }}</text>
      </svg>
      <div class="btn" @click="close"></div>
    </div>
    <div class="coupon-bg" v-else-if="prizeInfo.prizeType === 1">
      <img :src="prizeInfo?.prizeImg" alt="" class="prize-img" />
      <svg class="prize-name" viewBox="0 0 500 80" xmlns="http://www.w3.org/2000/svg">
        <text x="250" y="20" class="prize-name-text">恭喜您成功抽中</text>
        <text x="250" y="60" class="prize-name-text">{{ prizeInfo?.prizeName }}</text>
      </svg>
      <div class="btn" @click="toMyCoupon"></div>
    </div>
    <div class="e-card-bg" v-else-if="prizeInfo.prizeType === 7">
      <img :src="prizeInfo?.prizeImg" alt="" class="prize-img" />
      <svg class="prize-name" viewBox="0 0 500 80" xmlns="http://www.w3.org/2000/svg">
        <text x="250" y="20" class="prize-name-text">恭喜您成功抽中</text>
        <text x="250" y="60" class="prize-name-text">{{ prizeInfo?.prizeName }}</text>
      </svg>
      <div class="tip">
        E卡将于2025年10月13日后且无退款退货行为统一进行发放，<br />
        届时可在我的奖品中查看卡密
      </div>
      <div class="btn" @click="close"></div>
    </div>
    <div class="red-bag-bg" v-else-if="prizeInfo.prizeType === 99">
      <svg class="prize-name" viewBox="0 0 500 120" xmlns="http://www.w3.org/2000/svg">
        <text x="20" y="20" class="prize-name-text prize-name-text-title">恭喜您喜提</text>
        <text x="20" y="90" class="prize-name-text prize-name-text-content">
          实付金额
          <tspan style="font-size: 58px">x</tspan>
          <tspan style="font-size: 90px">{{ prizeInfo?.freeMultiple }}</tspan>
          倍免单
        </text>
      </svg>
      <div class="btn" @click="showShareCard"></div>
      <div class="tip">
        <div>免单金额将于2025年10月13日，且中奖订单所有商品均无退货退款行为后，统一进行发放</div>
        <div>如有随伊利活动范围内商品同一笔订单支付的，需全部商品收货后，第二天点击“我的奖品”查看全部免单金额</div>
      </div>
    </div>
    <div class="opportunities-bg" v-else-if="prizeInfo.prizeType === -1">
      <div class="btn" @click="close"></div>
    </div>
    <div class="no-win-bg" v-else-if="prizeInfo.prizeType === 0">
      <div class="btn" @click="close"></div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';

const props = defineProps(['prizeInfo']);

const emits = defineEmits(['close', 'showShareCard']);
const close = () => {
  emits('close');
};

const toMyCoupon = () => {
  window.jmfe.toMyCoupon();
};

const showShareCard = () => {
  emits('showShareCard', props.prizeInfo.shareCardRecordId);
};
</script>

<style scoped lang="scss">
.pop-bg {
  position: relative;
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
.jingdou-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/340590/31/2781/87948/68afc855Faffe720d/cc40d4cfe0979bfa.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.8rem;
  padding: 2.18rem 0.15rem 0 0.35rem;
  .prize-img {
    height: 1.55rem;
    width: 4rem;
    object-fit: contain;
    margin: 0 auto 0.15rem;
  }
  .prize-name {
    width: 5rem;
    height: 0.8rem;
    margin: 0 auto 0.15rem;
    display: block;
    .prize-name-text {
      font-size: 28px;
      text-anchor: middle;
      dominant-baseline: middle;
      fill: #fdd3a5;
      stroke: #d63e1c;
      stroke-width: 6;
      stroke-linejoin: round;
      font-weight: bold;
      paint-order: stroke;
      stroke-linecap: round;
    }
  }
  .btn {
    width: 1.95rem;
    height: 0.64rem;
    margin: 0 auto 0;
  }
}
.e-card-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/336442/16/2870/85117/68afc856F4e577405/d0a9eeee73833e8c.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.55rem;
  padding: 2.18rem 0.15rem 0 0.3rem;
  .prize-img {
    height: 1.55rem;
    width: 4rem;
    object-fit: contain;
    margin: 0 auto 0.15rem;
  }
  .prize-name {
    width: 5rem;
    height: 0.8rem;
    margin: 0 auto 0.15rem;
    display: block;
    .prize-name-text {
      font-size: 28px;
      text-anchor: middle;
      dominant-baseline: middle;
      fill: #fdd3a5;
      stroke: #d63e1c;
      stroke-width: 6;
      stroke-linejoin: round;
      font-weight: bold;
      paint-order: stroke;
      stroke-linecap: round;
    }
  }
  .tip {
    width: 4.63rem;
    height: 0.72rem;
    font-size: 0.167rem;
    margin: 0 auto 0.2rem;
    text-align: center;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .btn {
    width: 1.95rem;
    height: 0.64rem;
    margin: 0 auto 0;
  }
}
.coupon-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/332277/20/5378/85308/68afc856F5fb272c7/e9db937f2f9853c7.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.7rem;
  padding: 2.18rem 0.15rem 0 0.3rem;
  .prize-img {
    height: 1.6rem;
    width: 4rem;
    object-fit: contain;
    margin: 0 auto 0.15rem;
  }
  .prize-name {
    width: 5rem;
    height: 0.8rem;
    margin: 0 auto 0.15rem;
    display: block;
    .prize-name-text {
      font-size: 28px;
      text-anchor: middle;
      dominant-baseline: middle;
      fill: #fdd3a5;
      stroke: #d63e1c;
      stroke-width: 6;
      stroke-linejoin: round;
      font-weight: bold;
      paint-order: stroke;
      stroke-linecap: round;
    }
  }
  .btn {
    width: 1.95rem;
    height: 0.64rem;
    margin: 0 auto 0;
  }
}
.red-bag-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/325323/15/16002/85220/68b93d98F7c40264d/cd590fc35c6af090.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.8rem;
  padding: 2.18rem 0.15rem 0 0.3rem;
  .prize-name {
    width: 5rem;
    height: 1.2rem;
    margin: 0 0 0.3rem 0.67rem;
    display: block;
    .prize-name-text {
      font-size: 28px;
      text-anchor: start;
      dominant-baseline: middle;
      fill: #fdd3a5;
      stroke: #d63e1c;
      stroke-width: 6;
      stroke-linejoin: round;
      font-weight: bold;
      paint-order: stroke;
      stroke-linecap: round;
    }
    .prize-name-text-title {
    }
    .prize-name-text-content {
      font-size: 42px;
      stroke-width: 8;
      tspan {
        dominant-baseline: middle;
        fill: #f32e30;
        stroke-width: 0;
      }
    }
  }
  .btn {
    width: 3.43rem;
    height: 0.99rem;
    margin: 0 auto 0.15rem;
  }
  .tip {
    width: 5.29rem;
    height: 0.85rem;
    font-size: 0.14rem;
    line-height: 0.17rem;
    color: #fff;
    margin: 0 auto;
    padding: 0 0.05rem 0 0.4rem;
    word-break: break-all;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}
.opportunities-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/332952/34/5452/99698/68b025faF20c1bf31/d38d0b39152bde96.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.3rem;
  padding: 5.35rem 0.15rem 0 0.3rem;
  .btn {
    width: 1.95rem;
    height: 0.65rem;
    margin: 0 auto 0;
  }
}
.no-win-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/301984/22/22031/100831/68afc856Fd385833b/3e184eaf43ccd593.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.5rem;
  padding: 5.44rem 0.15rem 0 0.3rem;
  .btn {
    width: 1.95rem;
    height: 0.65rem;
    margin: 0 auto 0;
  }
}
</style>
