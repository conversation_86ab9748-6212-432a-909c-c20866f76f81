<template>
  <div class="pop-bg">
    <div class="btn" @click="close"></div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/338233/18/6327/118848/68b93c09F5f54e36a/1729ea0f5329ff18.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 8.4rem;
  position: relative;
  padding: 2rem 0.5rem 0 0.7rem;
  .btn {
    width: 2rem;
    height: 0.6rem;
    position: absolute;
    top: 6.8rem;
    left: 2.3rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
