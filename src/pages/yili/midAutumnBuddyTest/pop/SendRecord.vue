<template>
  <div class="pop-bg">
    <div class="content">
      <div class="row" v-for="(item, index) in userGroupSendRecord" :key="index">{{ dayjs(item.finishTime).format('YYYY年M月D日 HH:mm:ss') }} 组团完成，已发放{{ item.sendChanceNum }}次抽奖机会</div>
      <div class="no-data" v-if="userGroupSendRecord.length === 0">暂无数据~</div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import dayjs from 'dayjs';

const props = defineProps(['userGroupSendRecord']);
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/340335/33/3061/77913/68b0285eFdd767ace/7e99c3c2faf2bf68.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.25rem;
  position: relative;
  padding: 2.1rem 0.3rem 0 0.5rem;
  .content {
    height: 3.15rem;
    overflow-y: auto;
    padding-top: 0.2rem;
    .row {
      text-align: center;
      font-size: 0.21rem;
      color: #d94f26;
    }
    .no-data {
      text-align: center;
      color: #fdf2ab;
      font-size: 0.26rem;
      text-shadow: 0.01rem 0 0 #ab0001, -0.01rem 0 0 #ab0001, 0 0.01rem 0 #ab0001, 0 -0.01rem 0 #ab0001;
      font-style: italic;
      margin-top: 1rem;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
