<template>
  <div class="page-bg">
    <div class="kv-content">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/328595/15/17429/461069/68be5270F4063e5e7/36a0493ae8372ea8.png" alt="" class="kv-img" />
      <div class="btn-list">
        <div class="btn" @click="rulePop = true"></div>
        <div class="btn" @click="myPrizePop = true"></div>
        <div class="btn" @click="showShareCardRecordPop"></div>
      </div>
      <div class="winner-list swiper-container">
        <div class="swiper-wrapper">
          <div class="swiper-slide winner-item" v-for="item in winnerList" :key="item">{{ getWinnerText(item) }}</div>
        </div>
      </div>
    </div>
    <LotteryPrize @showShareCard="showShareCard" />
    <SkuList />
    <Team />
    <Task />
    <ShopList />
    <ShareCard ref="shareCardRef" />

    <VanPopup v-model:show="rulePop" teleport="body" :close-on-click-overlay="false">
      <Rule @close="rulePop = false" />
    </VanPopup>
    <VanPopup v-model:show="myPrizePop" teleport="body" :close-on-click-overlay="false">
      <MyPrize v-if="myPrizePop" @close="myPrizePop = false" @showCopyCard="showCopyCard" />
    </VanPopup>
    <VanPopup v-model:show="shareCardRecordPop" teleport="body" :close-on-click-overlay="false">
      <ShareCardRecord v-if="shareCardRecordPop" @close="shareCardRecordPop = false" @showShareCard="showShareCard" />
    </VanPopup>
    <VanPopup v-model:show="acceptCouponsPop" teleport="body" :close-on-click-overlay="false">
      <AcceptCoupons @close="acceptCouponsPop = false" />
    </VanPopup>
    <VanPopup v-model:show="activityGuidePop" teleport="body" :close-on-click-overlay="false">
      <ActivityGuide @close="activityGuidePop = false" />
    </VanPopup>
    <VanPopup v-model:show="copyCardPop" teleport="body" :close-on-click-overlay="false">
      <CopyCard v-if="copyCardPop" :copyCardData="copyCardData" @close="copyCardPop = false" />
    </VanPopup>
  </div>
</template>

<script lang="ts" setup>
import { nextTick } from 'process';
import LotteryPrize from './components/LotteryPrize.vue';
import SkuList from './components/SkuList.vue';
import Team from './components/Team.vue';
import Task from './components/Task.vue';
import ShareCard from './components/ShareCard.vue';
import ShareCardRecord from './pop/ShareCardRecord.vue';
import MyPrize from './pop/MyPrize.vue';
import ActivityGuide from './pop/ActivityGuide.vue';
import CopyCard from './pop/CopyCard.vue';
import ShopList from './components/ShopList.vue';
import AcceptCoupons from './pop/AcceptCoupons.vue';
import Swiper, { Autoplay } from 'swiper';
import Rule from './pop/Rule.vue';
import 'swiper/swiper.min.css';
import { inject, onMounted, ref, watch } from 'vue';
import { activityInfo, checkActStatus, winnerList } from './hooks';

const pathParams = inject('pathParams') as any;

Swiper.use([Autoplay]);
const rulePop = ref(false);
const myPrizePop = ref(false);
const shareCardRecordPop = ref(false);
const shareCardRef = ref();
const acceptCouponsPop = ref(false);
const activityGuidePop = ref(false);
const copyCardPop = ref(false);

const showShareCardRecordPop = () => {
  checkActStatus();
  shareCardRecordPop.value = true;
};

const showShareCard = (shareCardId: string) => {
  console.log(shareCardRef.value);
  shareCardRef.value.createShareCard(shareCardId);
};

const copyCardData = ref<any>({});
const showCopyCard = (data: any) => {
  copyCardData.value = data;
  copyCardPop.value = true;
  myPrizePop.value = false;
};

let swiper: any;
const initSwiper = () => {
  if (swiper) {
    swiper.destroy();
  }
  nextTick(() => {
    swiper = new Swiper('.winner-list', {
      direction: 'vertical',
      slidesPerView: 1,
      autoplay: {
        delay: 2000,
        disableOnInteraction: false,
      },
      loop: true,
    });
  });
};
watch(
  () => winnerList,
  (val) => {
    initSwiper();
  },
  {
    deep: true,
  },
);

onMounted(() => {
  initSwiper();
  try {
    checkActStatus(false);
    if (pathParams?.shareCardId) {
      acceptCouponsPop.value = true;
    }
    if (activityInfo.newUser && !pathParams?.shareCardId && !pathParams?.friendId) {
      activityGuidePop.value = true;
    }
  } catch (error) {}
});

const getWinnerText = (item: any) => {
  if (item.prizeType === 99) {
    return `恭喜用户${item.nickName}抽中${item.freeMultiple}倍免单`;
  }
  return `恭喜用户${item.nickName}抽中${item.prizeName}`;
};
</script>

<style scoped lang="scss">
.page-bg {
  min-height: 100vh;
  background: #fadeb0;
}
.kv-content {
  position: relative;
  margin-bottom: 0.15rem;
  .kv-img {
    width: 100%;
  }
  .btn-list {
    position: absolute;
    right: 0;
    top: 0.633rem;
    .btn {
      width: 0.98rem;
      height: 0.3rem;
      margin-bottom: 0.08rem;
    }
  }
  .winner-list {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 7rem;
    height: 0.4rem;
    overflow: hidden;
    .winner-item {
      height: 0.4rem;
      line-height: 0.4rem;
      text-align: center;
      font-size: 0.24rem;
      color: #cf0101;
      font-weight: bold;
    }
  }
}
</style>
