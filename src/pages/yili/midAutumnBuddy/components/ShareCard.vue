<template>
  <div class="share-card" id="share-card">
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336040/28/6680/949133/68b9402fF3352776b/82c272f06d683572.png" alt="" class="share-card-bg" />
    <img :src="userInfo.avatar" alt="" class="avatar" />
    <div class="nickname">{{ userInfo.nickname }}</div>
    <img :src="qrcodeUrl" alt="" class="qrcode" />
  </div>

  <VanPopup v-model:show="shareCardPop" teleport="body">
    <div class="share-card-pop-bg" :style="{ backgroundImage: `url(${shareCardUrl})` }">
      <img src="https://img10.360buyimg.com/imgzone/jfs/t1/333016/22/5968/20605/68b17449F72afd08e/e57d62ce6de48d1f.png" alt="" class="share-btn" @click="handleShare" />
    </div>
  </VanPopup>
</template>

<script lang="ts" setup>
import { nextTick } from 'process';
import QRCode from 'qrcode';
import { inject, ref } from 'vue';
import html2canvas from 'html2canvas';
import { closeToast, showLoadingToast } from 'vant';
import { callPosterShare } from '@/utils/platforms/share';
import { UserInfo } from '@/utils/products/types/UserInfo';
import { SharePosterConfig } from '@/types/ShareConfig';

const userInfo = inject('userInfo') as UserInfo;

const shareCardPop = ref(false);

const qrcodeUrl = ref('');
const shareCardUrl = ref('');

const createShareCard = async (shareCardId: string) => {
  const qrcode = await QRCode.toDataURL(`${process.env.VUE_APP_HOST}yili/midAutumnBuddy/?shareCardId=${shareCardId}`, {
    margin: 0,
  });
  qrcodeUrl.value = qrcode;
  nextTick(async () => {
    const shareCard = document.getElementById('share-card');
    if (shareCard) {
      try {
        const canvas = await html2canvas(shareCard, {
          useCORS: true,
          scale: 0.7,
        });
        shareCardUrl.value = canvas.toDataURL('image/png');
        shareCardPop.value = true;
      } catch (error) {
        console.error(error);
      }
    }
  });
};

const getPostConfig = (): SharePosterConfig => ({
  bg: {
    url: shareCardUrl.value,
  },
  content: {
    x: 67,
    y: 354,
    w: 616,
    h: 616,
  },
  shareUrl: `${process.env.VUE_APP_HOST}yili/midAutumnBuddy/`,
  quality: 1,
});

// 分享
const handleShare = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const shareConfig = JSON.parse(window.sessionStorage.getItem('LZ_SHARE_CONFIG') ?? '');
    // 海报分享
    await callPosterShare({
      title: shareConfig.shareTitle,
      content: shareConfig.shareContent,
      channel: 'QRCode',
      poster: getPostConfig(),
    });

    closeToast();
  } catch (error: any) {
    closeToast();
    console.error(error);
  }
};

defineExpose({
  createShareCard,
});
</script>

<style scoped lang="scss">
.share-card {
  position: fixed;
  top: -3000px;
  left: 0;
  // position: relative;
  .share-card-bg {
    width: 1080px;
  }
  .avatar {
    width: 118px;
    height: 118px;
    border-radius: 50%;
    position: absolute;
    top: 688px;
    left: 228px;
  }
  .nickname {
    position: absolute;
    top: 725px;
    left: 373px;
    font-size: 42px;
    color: #d63e1c;
    font-weight: bold;
    line-height: 42px;
  }
  .qrcode {
    width: 240px;
    height: 240px;
    position: absolute;
    top: 1285px;
    left: 655px;
  }
}
.share-card-pop-bg {
  width: 4.5rem;
  height: 9.8rem;
  background-size: 100%;
  background-repeat: no-repeat;
  position: relative;
  .share-btn {
    position: absolute;
    top: 6.4rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2rem;
  }
}
</style>
