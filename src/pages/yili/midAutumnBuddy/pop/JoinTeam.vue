<template>
  <div class="pop-bg" :class="status !== -1 ? 'disabled' : ''">
    <div class="content">
      <img :src="initiateInfo?.sponsorAvatar" alt="" class="avatar" />
      <svg class="prize-name" viewBox="0 0 500 80" xmlns="http://www.w3.org/2000/svg">
        <text x="250" y="20" class="prize-name-text">{{ initiateInfo?.sponsorName }}</text>
        <text x="250" y="60" class="prize-name-text">邀请您加入队伍，是否同意？</text>
      </svg>
      <div class="btn-join" @click="join"></div>
      <div class="btn-cancel" @click="close"></div>
    </div>
    <div class="mask">
      <img v-if="status === 0" src="https://img10.360buyimg.com/imgzone/jfs/t1/330737/20/6055/23360/68b16c6eF21a1c6f3/9ce5672ebd3f00fc.png" alt="" />
      <img v-else-if="status === 1" src="https://img10.360buyimg.com/imgzone/jfs/t1/328847/34/12768/23269/68b16c6eFf1992cc0/8061549414b1639b.png" alt="" />
      <img v-else-if="status === 2" src="https://img10.360buyimg.com/imgzone/jfs/t1/330720/21/5974/23997/68b16c6eF298644b7/33925f57527514c7.png" alt="" />
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject, ref } from 'vue';

const pathParams = inject('pathParams') as any;

const props = defineProps({
  initiateInfo: {
    type: Object,
    default: () => {},
  },
});
const emits = defineEmits(['close', 'success']);
const close = () => {
  emits('close');
};

const status = ref(-1);

const join = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    await httpRequest.post('/brand/yiLiMidAutumn/joinGroup', {
      friendId: pathParams.friendId,
    });
    closeToast();
    status.value = 0;
    emits('success');
  } catch (error: any) {
    closeToast();
    const { message } = error;
    if (message.includes('当前队伍已满员，无法加入!')) {
      status.value = 1;
    } else if (message.includes('您已存在其他参与队伍，无法加入其他队！')) {
      status.value = 2;
    } else {
      showToast(message);
      setTimeout(() => {
        close();
      }, 2000);
    }
  }
};
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/339650/21/3470/79376/68b1696aFa485a4d6/c4af8314786681e1.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.5rem;
  position: relative;
  padding: 2.18rem 0.15rem 0 0.35rem;
  .content {
    .avatar {
      width: 1.35rem;
      height: 1.35rem;
      border-radius: 50%;
      margin: 0 auto 0.1rem;
    }
    .prize-name {
      width: 5rem;
      height: 0.8rem;
      margin: 0 auto 0.05rem;
      display: block;
      .prize-name-text {
        font-size: 28px;
        text-anchor: middle;
        dominant-baseline: middle;
        fill: #fdd3a5;
        stroke: #d63e1c;
        stroke-width: 6;
        stroke-linejoin: round;
        font-weight: bold;
        paint-order: stroke;
        stroke-linecap: round;
      }
    }
    .btn-join {
      width: 2rem;
      height: 0.6rem;
      margin: 0 auto 0.15rem;
    }
    .btn-cancel {
      width: 2rem;
      height: 0.6rem;
      margin: 0 auto;
    }
  }
  .mask {
    display: none;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
    z-index: 31;
  }
}
.disabled {
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    mask: url('https://img10.360buyimg.com/imgzone/jfs/t1/339650/21/3470/79376/68b1696aFa485a4d6/c4af8314786681e1.png') no-repeat;
    mask-size: 100%;
    z-index: 30;
  }
  .mask {
    display: block;
    position: absolute;
    top: 2.6rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 32;
    img {
      width: 4.25rem;
    }
  }
}
</style>
