<template>
  <div class="pop-bg">
    <div class="content">
      <div class="row" v-for="item in recordList" :key="item.shareCardId">
        <div>{{ dayjs(item.createTime).format('MM-DD') }}</div>
        <div>{{ item.freeMultiple }}倍免单</div>
        <div><div class="btn" @click="showShareCard(item.shareCardId)">查看</div></div>
        <div>
          <p v-if="item.status === 0">未领取</p>
          <p v-else-if="item.status === 1">
            {{ item.receiveNickName }}<br />
            已领取
          </p>
          <p v-else-if="item.status === 2">领取失败</p>
        </div>
      </div>
      <div class="no-data" v-if="recordList.length === 0">暂无数据~</div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { ref } from 'vue';

const emits = defineEmits(['close', 'showShareCard']);
const close = () => {
  emits('close');
};

const showShareCard = (shareCardId: string) => {
  emits('showShareCard', shareCardId);
  close();
};

const recordList = ref<any[]>([]);

const getUserShareCardRecordList = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getUserShareCardRecordList');
    recordList.value = data;
    closeToast();
  } catch (error) {
    closeToast();
  }
};
getUserShareCardRecordList();
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/294269/17/26765/81848/68ae6fc7Fd7873281/c0d80b2c2e02ca97.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.45rem;
  position: relative;
  padding: 2.6rem 0.5rem 0 0.5rem;
  .content {
    height: 2.75rem;
    overflow-y: auto;
    padding-top: 0.2rem;
    .row {
      display: flex;
      align-items: center;
      margin-bottom: 0.25rem;
      div {
        width: 25%;
        text-align: center;
        color: #d23f1d;
        font-size: 0.2rem;
      }
      .btn {
        width: 1rem;
        height: 0.35rem;
        line-height: 0.35rem;
        border-radius: 0.175rem;
        background: #ff6233;
        color: #fff;
        font-size: 0.2rem;
        margin-left: 0.2rem;
      }
    }
    .no-data {
      text-align: center;
      color: #fdf2ab;
      font-size: 0.26rem;
      text-shadow: 0.01rem 0 0 #ab0001, -0.01rem 0 0 #ab0001, 0 0.01rem 0 #ab0001, 0 -0.01rem 0 #ab0001;
      font-style: italic;
      margin-top: 1rem;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
