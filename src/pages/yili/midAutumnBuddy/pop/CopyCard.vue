<template>
  <div class="pop-bg" v-if="!isShowRedemption">
    <div class="content">
      <div class="title">
        恭喜您成功兑换{{ copyCardData.prizeName }}<br />
        您的卡密如下，请前往兑换
      </div>
      <div class="item" v-if="copyCardData.cardNumber">
        <div class="text">{{ copyCardData.cardNumber && copyCardData.cardPassword ? '卡号：' : '' }}{{ copyCardData.cardNumber }}</div>
        <div class="copy-btn" :copy-text="copyCardData.cardNumber">复制</div>
      </div>
      <div class="item" v-if="copyCardData.cardPassword">
        <div class="text">{{ copyCardData.cardNumber && copyCardData.cardPassword ? '卡密：' : '' }}{{ copyCardData.cardPassword }}</div>
        <div class="copy-btn" :copy-text="copyCardData.cardPassword">复制</div>
      </div>
    </div>
    <div class="btn" @click="isShowRedemption = true"></div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
  <div class="redemption-process" v-else>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import Clipboard from 'clipboard';
import { showToast } from 'vant';

const props = defineProps(['copyCardData']);

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const isShowRedemption = ref(false);

const clipboard = new Clipboard('.copy-btn', {
  text(trigger) {
    return trigger.getAttribute('copy-text') ?? '';
  },
})
  .on('success', () => {
    showToast('复制成功');
  })
  .on('error', () => {
    showToast('复制失败');
  });
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/327635/14/16343/82563/68ba5e7cFf46f5e13/bee3f89779db2f3b.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.2rem;
  position: relative;
  padding: 2.13rem 0.5rem 0 0.7rem;
  .content {
    height: 1.96rem;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    .title {
      font-size: 0.25rem;
      font-weight: bold;
      text-align: center;
      color: #d53a19;
    }
    .item {
      width: 4.55rem;
      height: 0.52rem;
      margin: 0 auto;
      display: flex;
      align-items: center;
      background-color: #fff;
      border-radius: 0.1rem;
      font-size: 0.22rem;
      color: #333131;
      line-height: 0.52rem;
      font-weight: bold;
      .text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 0.2rem;
      }
      .copy-btn {
        width: 1.31rem;
        background-color: #ffc131;
        border-radius: 0 0.1rem 0.1rem 0;
        text-align: center;
      }
    }
  }
  .btn {
    position: absolute;
    top: 5.2rem;
    left: 50%;
    transform: translateX(-50%);
    width: 2.11rem;
    height: 0.62rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
.redemption-process {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/334331/36/9772/86483/68ba5e7bF2e1b23d0/9b5f3a172b6a9944.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.7rem;
  position: relative;
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
