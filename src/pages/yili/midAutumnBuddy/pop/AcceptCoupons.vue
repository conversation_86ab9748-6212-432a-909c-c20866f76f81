<template>
  <div class="pop-bg" :class="{ disabled: status !== 0 }" v-if="!success">
    <div class="content">
      <div class="btn-join" @click="receiveShareCard"></div>
    </div>
    <div class="mask">
      <img v-if="status === 1" src="https://img10.360buyimg.com/imgzone/jfs/t1/333698/3/5979/23694/68b17292F5eeecc75/733bfd3e652d9b0c.png" alt="" />
      <img v-else-if="status === 2" src="https://img10.360buyimg.com/imgzone/jfs/t1/336972/1/3511/23883/68b1729aF1c20a6c7/eea0cbadabcefdf2.png" alt="" />
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
  <div class="success-pop" v-else>
    <div class="btn-join" @click="toMyCoupon"></div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import { closeToast, showLoadingToast, showToast } from 'vant';
import { inject, ref } from 'vue';

const pathParams = inject('pathParams') as any;

const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};

const status = ref(0);
const success = ref(false);

const receiveShareCard = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/receiveShareCard', {
      shareCardId: pathParams.shareCardId,
    });
    closeToast();
    if (data.status === 1) {
      // showToast('领取成功');
      success.value = true;
    } else {
      showToast('领取失败');
    }
  } catch (error: any) {
    closeToast();
    const { message } = error;
    if (message.includes('很抱歉，当前优惠券已被领取')) {
      status.value = 1;
    } else if (message.includes('无法领取自己的分享卡哦')) {
      status.value = 2;
    } else {
      showToast(message);
      setTimeout(() => {
        close();
      }, 2000);
    }
  }
};

const toMyCoupon = () => {
  window.jmfe.toMyCoupon();
};
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/335031/10/5989/100652/68b17286F4be3b048/d48f1fcc76713d73.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.5rem;
  position: relative;
  padding: 5.4rem 0.15rem 0 0.35rem;
  .content {
    .avatar {
      width: 1.35rem;
      height: 1.35rem;
      border-radius: 50%;
      margin: 0 auto 0.1rem;
    }
    .prize-name {
      width: 5rem;
      height: 0.8rem;
      margin: 0 auto 0.05rem;
      display: block;
      .prize-name-text {
        font-size: 28px;
        text-anchor: middle;
        dominant-baseline: middle;
        fill: #fdd3a5;
        stroke: #d63e1c;
        stroke-width: 6;
        stroke-linejoin: round;
        font-weight: bold;
        paint-order: stroke;
        stroke-linecap: round;
      }
    }
    .btn-join {
      width: 2rem;
      height: 0.6rem;
      margin: 0 auto 0.15rem;
    }
  }
  .mask {
    display: none;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
    z-index: 31;
  }
}
.disabled {
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    mask: url('https://img10.360buyimg.com/imgzone/jfs/t1/335031/10/5989/100652/68b17286F4be3b048/d48f1fcc76713d73.png') no-repeat;
    mask-size: 100%;
    z-index: 30;
  }
  .mask {
    display: block;
    position: absolute;
    top: 2.6rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 32;
    img {
      width: 4.25rem;
    }
  }
}
.success-pop {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/331416/22/7782/99091/68b65276F794a8458/926c1d2a4737a01d.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.5rem;
  position: relative;
  padding: 5.4rem 0.15rem 0 0.35rem;
  .btn-join {
    width: 2rem;
    height: 0.6rem;
    margin: 0 auto 0.15rem;
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
    z-index: 31;
  }
}
</style>
