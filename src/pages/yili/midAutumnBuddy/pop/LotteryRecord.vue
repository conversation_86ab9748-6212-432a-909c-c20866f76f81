<template>
  <div class="pop-bg">
    <div class="content">
      <div class="row" v-for="item in list" :key="item.chanceId">
        <div>{{ chanceSourceMap[item.chanceSource] }}</div>
        <div>{{ getSourceDetail(item) }}</div>
        <div>{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
        <div>
          <div class="btn" @click="toLottery(item.chanceId)" v-if="item.chanceUsed === 0">使用</div>
          <div class="btn gary" v-else>已使用</div>
        </div>
      </div>
      <div class="no-data" v-if="list.length === 0">暂无数据~</div>
    </div>
    <img src="https://img10.360buyimg.com/imgzone/jfs/t1/336869/14/1582/2854/68ac366aF0be259a4/00e52263d8edcc17.png" alt="" class="close" @click="close" />
  </div>
</template>

<script lang="ts" setup>
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { closeToast, showLoadingToast } from 'vant';
import { onMounted, ref } from 'vue';

const emits = defineEmits(['close', 'toLottery']);
const close = () => {
  emits('close');
};
const toLottery = (chanceId: string) => {
  emits('toLottery', chanceId);
};

const chanceSourceMap: Record<number, string> = {
  1: '下单',
  2: '其他',
  3: '其他',
  4: '拼团',
};

const getSourceDetail = (item: any) => {
  if (item.chanceSource === 1) {
    return item.parentOrderId;
  }
  if (item.chanceSource === 2) {
    return '入会任务';
  }
  if (item.chanceSource === 3) {
    return '加购任务';
  }
  if (item.chanceSource === 4) {
    return '拼团奖励';
  }
};

const list = ref<any[]>([]);

const getUserChanceLogList = async () => {
  try {
    showLoadingToast({
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/brand/yiLiMidAutumn/getUserChanceLogList');
    closeToast();
    list.value = data;
  } catch (error) {
    closeToast();
  }
};

onMounted(() => {
  getUserChanceLogList();
});
</script>

<style scoped lang="scss">
.pop-bg {
  background: url('https://img10.360buyimg.com/imgzone/jfs/t1/326822/7/15903/85324/68b93c82F6c8a60cf/6c0b077d1722c028.png') no-repeat;
  background-size: 100%;
  width: 6.4rem;
  height: 7.45rem;
  position: relative;
  padding: 2.6rem 0.6rem 0 0.6rem;
  .content {
    height: 2.4rem;
    overflow-y: auto;
    padding-top: 0.2rem;
    .row {
      display: flex;
      align-items: center;
      margin-bottom: 0.25rem;
      div {
        width: 25%;
        text-align: center;
        color: #d23f1d;
        font-size: 0.2rem;
      }
      .btn {
        width: 1rem;
        height: 0.35rem;
        line-height: 0.35rem;
        border-radius: 0.175rem;
        background: #ff6233;
        color: #fff;
        font-size: 0.2rem;
        margin-left: 0.07rem;
      }
      .gary {
        background: #bfb288;
        color: #c90102;
      }
    }
    .no-data {
      text-align: center;
      color: #fdf2ab;
      font-size: 0.26rem;
      text-shadow: 0.01rem 0 0 #ab0001, -0.01rem 0 0 #ab0001, 0 0.01rem 0 #ab0001, 0 -0.01rem 0 #ab0001;
      font-style: italic;
      margin-top: 1rem;
    }
  }
  .tip {
    font-size: 0.18rem;
    text-align: center;
    span {
      background-color: #e99062;
      color: #fff;
      line-height: 0.3rem;
      display: inline-block;
    }
  }
  .close {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0.75rem;
  }
}
</style>
