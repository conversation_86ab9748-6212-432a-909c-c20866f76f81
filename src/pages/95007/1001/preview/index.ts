import { createApp } from 'vue';
import { initRem } from '@/utils/platforms/client';
import { initPreview } from '@/utils';
import index from './Index.vue';
import IAmorLottery from 'iamor-lottery-vue';
import { InitRequest } from '@/types/InitRequest';
import '../style';
import '@/style';

initRem();

const app = createApp(index);
app.use(IAmorLottery);
// 初始化页面
const config: InitRequest = {
  // templateCode: '1663383671591972867',
};
const _decoData = {
  actBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/234201/38/21429/122901/66a06cbaF9f6f3f95/0a033ee8d01dc447.png',
  pageBg: 'https://img10.360buyimg.com/imgzone/jfs/t1/56584/38/24696/34174/66a077ddF984b3d03/a37690494de5c9d6.png',
  mpImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/223251/20/37551/187602/66a06d72Faa3b6880/ad146ff545be61a1.png',
  cmdImg: 'https://img10.360buyimg.com/imgzone/jfs/t1/233009/27/20980/139107/66a06d7dFe817099e/5701b5a373f2d2b0.png',
  h5Img: 'https://img10.360buyimg.com/imgzone/jfs/t1/228822/13/23123/21007/66a06d61F20110d30/77a7fa8f6cfd9d42.png',
};
initPreview(config).then(({ pathParams, activityData, decoData }) => {
  document.title = activityData?.activityName || '会员试用';
  app.provide('pathParams', pathParams);
  app.provide('activityData', activityData);
  app.provide('decoData', decoData);
  app.provide('isPreview', true);
  app.mount('#app');
});
