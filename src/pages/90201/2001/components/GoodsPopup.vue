<template>
  <div class="rule-bk" :style="furnishStyles.goodsBk.value" :class="{ 'short-bk': orderSkuisExposure === 0 }">
    <div class="content" :class="{ 'short-content': orderSkuisExposure === 0 }">
      <div class="skuListClass" v-if="orderSkuisExposure !== 0">
        <div v-for="(item, index) in skuList.length ? skuList : data" class="skuItem" :key="index" @click="goToSku(item)">
          <div class="skuImgBox">
            <img class="skuImg" :src="item.skuMainPicture" alt="" />
          </div>
          <div class="skuName" v-text="item.skuName"></div>
          <div class="btm-box">
            <div class="jdPrice">¥ <span v-text="item.jdPrice"></span></div>
            <div class="bug-btn">立即购买</div>
          </div>
        </div>
        <div class="more-btn" v-if="!isPreview && skuList.length && skuList.length < total" @click="loadMore">点我加载更多</div>
        <div class="more-btn" v-if="isPreview && data.length && data.length !== total" @click="loadMorePreview">点我加载更多</div>
        <!--          <div class="no-more" v-if="skuList.length > 4 || data.length > 4">—— 没有更多了 ——</div>-->
      </div>
      <div v-else class="no-data">活动商品为本店全部商品</div>
    </div>
    <img :src="furnish?.goodsToShopBtn ?? '//img10.360buyimg.com/imgzone/jfs/t1/257658/26/18906/3295/67ab0468F2977c92e/9430aa88ca4a2758.png'" alt="" @click="gotoShopPage(baseInfo.shopId)" class="to-shop-btn" />
  </div>
</template>

<script lang="ts" setup>
import { gotoShopPage, gotoSkuPage } from '@/utils/platforms/jump';
import { ref, defineEmits, defineProps, inject, watch } from 'vue';
import { showLoadingToast, closeToast, showToast } from 'vant';
import { httpRequest } from '@/utils/service';
import { isPreview } from '@/utils';
import { BaseInfo } from '@/types/BaseInfo';
import furnishStyles, { furnish } from '../ts/furnishStyles';

const baseInfo = inject('baseInfo') as BaseInfo;
const props = defineProps(['data', 'orderSkuisExposure']);
const emits = defineEmits(['close']);
const close = () => {
  emits('close');
};
const skuList = ref<any[]>([]);
const pageNum = ref(0);
const total = ref(0);
let cursorNum = 0;
// 获取曝光商品
const getSkuList = async () => {
  showLoadingToast({
    message: '加载中...',
    forbidClick: true,
    duration: 0,
  });
  try {
    const { data } = await httpRequest.post('/90201/orderSku', {
      type: 1,
      pageNum: pageNum.value + 1,
      pageSize: 10,
      cursorNum,
    });
    skuList.value.push(...data.records);
    total.value = data.total;
    pageNum.value = data.current;
    cursorNum = data.current;
    closeToast();
  } catch (error: any) {
    closeToast();
  }
};
const goToSku = (item: any) => {
  if (isPreview) {
    showToast('活动预览,仅供查看');
    return;
  }
  gotoSkuPage(item.skuId);
};

const loadMore = async () => {
  await getSkuList();
};
const loadMorePreview = () => {
  showToast('活动预览,仅供查看');
};
if (!isPreview) {
  getSkuList();
} else {
  watch(props.data, () => {
    skuList.value = props.data;
  });
}
</script>

<style scoped lang="scss">
.rule-bk {
  height: 12.45rem;
  background: url(https://img10.360buyimg.com/imgzone/jfs/t1/252583/23/17622/17301/67a581cbF8fe8ca4e/4da29e8d0361cec2.png);
  background-size: 100%;
  background-repeat: no-repeat;
  padding-top: 0.8rem;

  .close {
    margin: 0.34rem auto 0;
    width: 0.5rem;
  }

  .content {
    height: 10.4rem;
    padding: 0.1rem;
    overflow-y: scroll;
    font-size: 0.24rem;
    color: #333333;
    white-space: pre-wrap;
    .skuListClass {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 0 auto;
      width: 7.15rem;
      .skuItem {
        background-color: #ffffff;
        margin-bottom: 0.2rem;
        width: 3.4rem;
        border-radius: 0.2rem;
        overflow: hidden;
        //padding: 0.2rem 0.4rem 0 0.4rem;
        .skuImgBox {
          display: flex;
          justify-content: center;
          .skuImg {
            width: 100%;
            height: 100%;
          }
        }
        .skuName {
          font-size: 0.28rem;
          margin: 0.1rem 0.2rem;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .btm-box {
          margin: 0.15rem 0.2rem;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
        .jdPrice {
          color: #dc0101;
          //text-align:right;
          font-size: 0.28rem;
          font-weight: bold;
          span {
            font-size: 0.38rem;
          }
        }
        .bug-btn {
          background-color: #dc0101;
          color: #fff;
          height: 0.4rem;
          line-height: 0.4rem;
          border-radius: 0.2rem;
          padding: 0 0.15rem;
          font-size: 0.25rem;
        }
      }
    }
    .no-more {
      margin: 0 auto;
      padding-top: 0.3rem;
      font-size: 0.2rem;
    }
  }
  .short-content {
    height: 2rem;
  }
}
.short-bk {
  height: 3.8rem;
}
.no-data {
  text-align: center;
  padding-top: 1rem;
  font-size: 0.3rem;
}
.to-shop-btn {
  width: 2.7rem;
  margin: 0.2rem auto 0;
}
.more-btn {
  width: 1.8rem;
  height: 0.5rem;
  font-size: 0.2rem;
  color: #fff;
  background-color: #000;
  border-radius: 0.25rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto 0.3rem;
}
</style>
