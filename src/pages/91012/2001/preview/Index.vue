<template>
  <div class="bg" :style="furnishStyles.pageBg.value" v-if="isLoadingFinish">
    <div class="header-kv">
      <img
        :src="
          furnish.actBg ??
          'https://img10.360buyimg.com/imgzone/jfs/t1/301423/11/18784/601352/6862381eF7c157842/845d970e4c4050d4.png'
        "
        alt=""
        class="kv-img"
      />
      <div class="header-content">
        <div class="shop-name" :style="furnishStyles.shopNameColor.value"></div>
        <div>
          <div
            class="header-btn"
            :style="furnishStyles.headerBtnRules.value"
            @click="showRulePop"
          />
          <div
            class="header-btn"
            :style="furnishStyles.headerBtnMyPrizes.value"
            @click="showMyPrizePop"
          />
        </div>
      </div>
    </div>

    <!-- 下单权益 -->
    <div class="qyDivTwoAll" :style="{ backgroundImage: `url(${furnish.qy2Bg})` }">
      <!-- <img :src="furnish.qy2Bg" alt="" /> -->
      <div class="qyTwoListDiv">
        <div class="qyTwoItemDiv" v-for="(it, index) in prizeList1" :key="index">
          <div class="restNum">剩余数量：{{ it.sendTotalCount }}</div>
          <div class="qyTwoBtn">立即领取</div>
        </div>
      </div>
    </div>

    <!-- 会员权益 -->
    <div class="qyDivOneAll" :style="{ backgroundImage: `url(${furnish.qy1Bg})` }">
      <!-- 立即领取按钮如果符合首购条件则显示，否则置灰 -->
      <div class="getQyBtn" @click="ShowToast"></div>
    </div>

    <div class="qyDivThreeAll">
      <!-- 动态生成的热区按钮 -->
      <HotZone :width="6.88" :data="furnish.hotZoneSetting" reportKey="" />
    </div>

    <div class="sku" v-if="isExposure === 1">
      <img class="sku-list-img" :src="furnish.showSkuBg" alt="" />
      <div class="sku-list">
        <div class="sku-list1">
          <div
            class="sku-item"
            v-for="(item, index) in skuList"
            :key="index"
            @click="ShowToast"
          ></div>
        </div>
      </div>
    </div>

    <div>
      <!-- 非会员拦截 -->
      <OpenCard :showPopup="showOpenCard" @closeDialog="showOpenCard = false" />
      <VanPopup teleport="body" v-model:show="showRule">
        <RulePopup :rule="ruleTest" @close="showRule = false" />
      </VanPopup>
      <VanPopup teleport="body" v-model:show="showMyPrize">
        <MyPrize @close="showMyPrize = false" />
      </VanPopup>

         <!-- 展示卡密 -->
    <VanPopup teleport="body" v-model:show="copyCardPopup">
      <CopyCard :detail="cardDetail" @close="copyCardPopup = false" />
    </VanPopup>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, inject, onMounted, nextTick, reactive } from "vue";
import dayjs from "dayjs";
import furnishStyles, { furnish } from "../ts/furnishStyles";
import { defaultStateList } from "../ts/default";
import RulePopup from "../components/RulePopup.vue";
import MyPrize from "../components/MyPrize.vue";
import usePostMessage from "@/hooks/usePostMessage";
import { showToast } from "vant";
import html2canvas from "html2canvas";
import HotZone from "../components/HotZone.vue";
import OpenCard from "../components/OpenCard.vue";
import SaveAddress from "../components/SaveAddress.vue";
import CopyCard from "../components/CopyCard.vue";

// 展示卡密
const copyCardPopup = ref(false);
const cardDetail = reactive({
  id: 1,
  prizeName: "",
  prizeImg: "",
  cardDesc: "",
  cardNumber: "123123",
  cardPassword: "**********",
  exchangeImg: "",
});
// 门槛弹窗
const showOpenCard = ref(false);
const prizeList1 = ref<any>([{},{},{},{},{},{}]);
const activityData = inject("activityData") as any;
const decoData = inject("decoData") as any;
const { registerHandler } = usePostMessage();

const endTime = ref(0);
const isStart = ref(false);
const startTime = ref(0);

const isLoadingFinish = ref(false);

type Sku = {
  skuName?: string;
  skuMainPicture?: string;
  jdPrice: string | number;
  showSkuImage: string;
};
const skuList = ref<Sku[]>([
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/282260/34/8580/199605/67e146ecF5d20dc7c/02ea93bfd09190c6.png",
  },
  {
    jdPrice: 99.99,
    showSkuImage:
      "//img10.360buyimg.com/imgzone/jfs/t1/274757/15/9027/250144/67e146ebFaee58006/d3acc37e59dfa4aa.png",
  },
]);
const orderSkuListPreview = ref<Sku[]>([]);
const shopName = ref("");

const showLimit = ref(false);
const showRule = ref(false);
const showMyPrize = ref(false);
const ruleTest = ref("");
const showGoods = ref(false);

const showRulePop = () => {
  showRule.value = true;
};

const showMyPrizePop = () => {
  showMyPrize.value = true;
};

const isExposure = ref(1);

const close = () => {
  showLimit.value = false;
};

// 页面截图
const isCreateImg = ref(false);
const dataURLToBlob = (dataurl: any) => {
  const arr = dataurl.split(",");
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  const n = bstr.length;
  const u8arr = new Uint8Array(n);
  for (let i = 0; i < bstr.length; i += 1) {
    u8arr[i] = bstr.charCodeAt(i);
  }
  return new Blob([u8arr], { type: mime });
};

const createImg = async () => {
  showRule.value = false;
  showMyPrize.value = false;
  showLimit.value = false;
  isCreateImg.value = true;
  nextTick(async () => {
    const canvas = await html2canvas(document.body, {
      useCORS: true,
      backgroundColor: null,
      scale: 1,
    });
    // 创建一个新的canvas来裁剪图片
    const cropCanvas = document.createElement("canvas");
    const ctx = cropCanvas.getContext("2d");
    cropCanvas.width = 375;
    cropCanvas.height = 670;
    // drawImage的参数为 source，sourceX, sourceY, sourceWidth, sourceHeight, destX, destY, destWidth, destHeight
    // 因为你没有指定从哪个位置开始裁剪，我默认为从(0, 0)位置开始
    ctx?.drawImage(
      canvas,
      0,
      0,
      canvas.width,
      (canvas.width / 375) * 670,
      0,
      0,
      375,
      670
    );
    // 获取裁剪后的图片
    const croppedBase64 = cropCanvas.toDataURL("image/png");
    isCreateImg.value = false;
    const blob = dataURLToBlob(croppedBase64);
    window.top?.postMessage(
      {
        from: "C",
        type: "screen",
        event: "sendScreen",
        data: blob,
      },
      "*"
    );
  });
};

const ShowToast = () => {
  showToast("活动预览，仅供查看");
};

// 装修数据监听
registerHandler("deco", (data) => {
  Object.keys(data).forEach((item) => {
    furnish[item] = data[item];
  });
  isLoadingFinish.value = true;
});
// 活动数据监听
registerHandler("activity", (data) => {
  // console.log("activity==========预览数据", data);
  endTime.value = dayjs(data.endTime).valueOf();
  if (data.prizeListOne && data.prizeListOne.length > 0) {
    prizeList1.value = data.prizeListOne;
  }
  startTime.value = new Date(data.startTime).getTime();
  if (startTime.value > new Date().getTime()) {
    isStart.value = false;
  }
  if (startTime.value < new Date().getTime()) {
    isStart.value = true;
  }
  endTime.value = new Date(data.endTime).getTime();
  if (data.skuList) {
    skuList.value = data.skuList;
  }
  if (data.orderSkuListPreview) {
    orderSkuListPreview.value = data.orderSkuListPreview;
  }
  ruleTest.value = data.rules;
  isExposure.value = data.isExposure;
  // console.log(skuList.value, "skuList.value========");
});
// 店铺信息监听
registerHandler("shop", (data: string) => {
  shopName.value = data;
});

// 截图监听
registerHandler("screen", () => {
  createImg();
});

onMounted(() => {
  if (activityData) {
    if (activityData.prizeListOne && activityData.prizeListOne.length > 0) {
      prizeList1.value = activityData.prizeListOne;
    }
    ruleTest.value = activityData.rules;
    orderSkuListPreview.value = activityData.orderSkuListPreview;
    shopName.value = activityData.shopName;
    skuList.value = activityData.skuList;
    isExposure.value = activityData.isExposure;
  }
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
    isLoadingFinish.value = true;
  }
  // console.log(skuList.value, "skuList.value========aaaaaaaaaaaa");
});
</script>
<style lang="scss" scoped>
.bg {
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
}

.header-kv {
  position: relative;
  //margin-bottom: 9rem;

  .kv-img {
    width: 100%;
  }

  .header-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    padding: 0.2rem 0 0 0.3rem;
    display: flex;
    justify-content: space-between;
  }
  .shop-name {
    font-size: 0.24rem;
  }

  .header-btn {
    width: 1.37rem;
    height: 0.57rem;
    margin-bottom: 0.1rem;
    font-size: 0.2rem;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100%;
  }
}

.qyDivOneAll {
  background-image: url("https://img10.360buyimg.com/imgzone/jfs/t1/320296/10/12113/43200/68623822Faf5d3658/ba54582c6181f8d7.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 6.88rem;
  height: 3.5rem;
  margin-left: 50%;
  transform: translateX(-50%);
  position: relative;
  .getQyBtn {
    background: url(//img10.360buyimg.com/imgzone/jfs/t1/231990/27/34519/3172/675fcb52F1895689a/96d5dc6d7eba624c.png)
      no-repeat;
    background-size: 100% 100%;
    width: 0.93rem;
    height: 1.9rem;
    display: flex;
    align-items: center;
    color: #fff;
    text-align: center;
    font-size: 0.28rem;
    padding: 0.1rem 0.2rem 0.1rem 0.1rem;
    box-sizing: border-box;
    position: absolute;
    right: 0.25rem;
    top: 1.1rem;
    filter: grayscale(1);
  }
}
.qyDivTwoAll {
  width: 6.88rem;
  margin: 0.3rem auto;
  position: relative;
  padding-bottom: 0.2rem;
  height: auto; /* 高度由内容或背景图决定（但需结合其他属性） */
  background-size: 100% auto; /* 宽度100%容器，高度按比例自动适应 */
  background-repeat: no-repeat;
  // background-position: center;
  // min-height: 3.2rem;
  height: 5.7rem;
  img {
    position: absolute;
    width: 6.88rem;
  }
  .qyTwoListDiv {
    position: relative;
    display: flex;
    align-items: center;
    padding: 1.1rem 0.2rem 0 0.2rem;
    margin: 0 auto;
    // justify-content: space-between;
    flex-wrap: wrap;
    .qyTwoItemDiv {
      position: relative;
      width: 2.1rem;
      height: 2.14rem;
      margin: 0 0.02rem;
      .restNum {
        width: 100%;
        text-align: center;
        position: absolute;
        bottom: 0.44rem;
        font-size: 0.18rem;
        color: #212121;
        font-weight: 700;
        left: 50%;
        transform: translateX(-50%);
      }
      .qyTwoBtn {
        background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/236315/36/9564/5513/658d5abdFd9e2fa3e/597a0717a8f07837.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;
        position: absolute;
        // background: #c56f26;
        width: 1.12rem;
        height: 0.33rem;
        font-size: 0;
        // left: 3.15rem;
        left: 50%;
        transform: translateX(-50%);
        bottom: 0.1rem;
        filter: grayscale(1);
      }
    }
  }
}
.qyDivThreeAll {
  margin-top: 0.6rem;
}
.sku {
  width: 7.21rem;
  padding: 0.2rem 0;
  position: relative;
  margin: 0.4rem auto 0.1rem auto;
  position: relative;
  // background-color: #000;
  .sku-list-img {
    width: 7.21rem;
    height: auto;
    position: absolute;
    top: 0;
    // background-color: #f2270c;
  }
  .sku-list {
    width: 7.25rem;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-gap: 0.2rem 0.2rem;
    // background-color: aqua;
    position: relative;
    z-index: 1;
    .sku-list1 {
      padding-top: 0.9rem;
      width: 7.25rem;
      height: 100%;
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: auto;
      grid-gap: 0.2rem 0.2rem;
      // background-color: #c56f26;
    }
  }
  .sku-item {
    width: 3.3rem;
    height: 4rem;
    overflow: hidden;
    margin-bottom: 0.2rem;
    .sku-text {
      display: flex;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      font-size: 0.32rem;
      height: 0.8rem;
      margin: 0.4rem auto 0;
      box-sizing: border-box;
      .go-sku-btn {
        width: 1.4rem;
        height: 0.3rem;
        //background-color: #000;
      }
    }
  }
}
</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
}
</style>
