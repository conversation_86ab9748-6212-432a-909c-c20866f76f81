<template>
  <div class="box">
    <div class="dialog">
      <div class="title">
        尊敬的用户您好，<br />恭喜您成功领取了{{
          detail.prizeName
        }}，<br />请复制卡密自行前往相对应的APP兑换
      </div>
      <div class="dialog_rule">
        <div>
          <div class="conten">
            <!-- 恭喜您成功领取{{ detail.prizeName }}权益，您的卡密是： -->
            <div class="item" v-show="detail.cardNumber">
              <div class="copy-btn" :copy-text="detail.cardNumber">复制卡号</div>
              <div class="text">{{ detail.cardNumber }}</div>
            </div>

            <div class="item" v-show="detail.cardPassword">
              <div class="copy-btn" :copy-text="detail.cardPassword">复制卡密</div>
              <div class="text">{{ detail.cardPassword }}</div>
            </div>
            
            <!-- 请前往兑换 -->
          </div>
          <!-- <div class="tips">
            注：卡密充值请仔细核实充值账号，确认无误，本产品属于虚拟产品，官方明确充值成功后无法退换和转移。兑换手机号请确认已注册平台账号，否则激活失败不负责赔偿。
          </div> -->
          <div class="confirm" @click="close()"></div>
        </div>
      </div>
    </div>
  </div>
  <!-- <ShowImage
    :showPopup="isShowImageDialog"
    :picUrl="detail.exchangeImg"
    @closeDialog="isShowImageDialog = false"
  ></ShowImage> -->
</template>

<script lang="ts" setup>
import { PropType, ref } from "vue";
import { CardType } from "../ts/type";
import Clipboard from "clipboard";
import { showToast } from "vant";
import ShowImage from "./ShowImage.vue";

const isShowImageDialog = ref(false);

const props = defineProps({
  detail: {
    type: Object as PropType<CardType>,
    required: true,
  },
});

const emits = defineEmits(["close"]);

const close = () => {
  emits("close");
};

console.log(";", props.detail);

const clipboard = new Clipboard(".copy-btn", {
  text(trigger) {
    return trigger.getAttribute("copy-text") ?? "";
  },
})
  .on("success", () => {
    showToast("复制成功");
  })
  .on("error", () => {
    showToast("复制失败");
  });
  
</script>

<style scoped lang="scss">
.box {
  .dialog {
    width: 6.52rem;
    margin: 0 auto;
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/296871/9/25897/11911/6881af0bFabeaa4d4/5e9f897d2158079e.png)
      no-repeat;
    height: 8.05rem;
    background-size: cover;
    box-sizing: border-box;
    padding: 1.3rem 0.1rem;
    .title {
      font-size: 0.28rem;
      text-align: center;
      font-weight: bold;
      letter-spacing: 0.08rem;
      color: #027854;
      margin: 0 0 0.1rem;
    }
    .dialog_rule {
      max-height: 3.73rem;
      font-size: 0.21rem;
      font-weight: normal;
      letter-spacing: 0.01rem;
      color: #027854;
      margin-top: 0.1rem;
      text-align: left;
      .conten {
        font-size: 0.26rem;
        font-weight: bold;
        word-wrap: break-word;
        margin-top: 0.5rem;
      }
      .item {
        display: flex;
        align-items: center;
        font-size: 0.24rem;
        margin-bottom: 0.1rem;
        border: 1px solid #017956;
        border-radius: 0.12rem;
        background-color: #fff;
        width: 5.85rem;
        height: 0.72rem;
        padding: 0.16rem 0;
        .label {
          color: #ffce5c;
          width: 0.8rem;
        }
        .text {
          flex: 1;
          color: #000;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          // border: 0.03rem solid #ffce5c;
          padding: 0.05rem 0.3125rem;
          line-height: 0.35rem;
          border-radius: 0.15rem 0 0 0.15rem;
        }
        .copy-btn {
          background-color: rgb(1, 121, 86);
          width: 1.1rem;
          height: 0.41rem;
          color: #fff;
          line-height: 0.41rem;
          box-sizing: border-box;
          font-size: 0.24rem;
          margin-right: 0.15rem;
          border-radius: 0.2rem;
          text-align: center;
        }
      }
      .tips {
        font-size: 0.22rem;
        padding: 0 0.1rem;
        text-align: left;
        box-sizing: border-box;
      }
      .confirm {
        background: url(https://img10.360buyimg.com/imgzone/jfs/t1/230512/17/19496/8629/665851daFf7afa617/671bfbe627e9480c.png)
          no-repeat;
        background-size: 100% 100%;
        width: 3.08rem;
        height: 0.9rem;
        margin: auto;
        position: absolute;
        left: calc(50% - 1.54rem);
        bottom: 1rem;
      }
    }
  }
}
</style>
