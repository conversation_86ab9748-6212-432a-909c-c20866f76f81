<template>
  <div class="bg" :style="furnishStyles.pageBg.value">
    <div class="header-content">
      <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'hdgz'" @click="showRulePopup">活动规则</div>
      <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'wdjp'" @click="showMyPrize = true">我的奖品</div>
      <div class="header-btn" :style="furnishStyles.headerBtn.value" v-click-track="'jdgg'" @click="gotoShopPage(baseInfo.shopId)">进店逛逛</div>
    </div>
    <div class="prizeBox">
      <img
        src="//img10.360buyimg.com/imgzone/jfs/t1/336848/5/7010/385/68bea3b9F36faf59e/aa1c9da47edd914e.png"
        alt=""
        class="leftBtn"
        @click=""
        v-if="prizeImgList?.length > 3"
      />
      <div class="prizeListClass">
        <div class="prizeItem" v-for="(imgItem, imgIndex) in prizeImgList" :key="imgIndex">
          <img :src="imgItem" alt="">
        </div>
      </div>
      <img src="//img10.360buyimg.com/imgzone/jfs/t1/340633/18/8430/374/68bea3b9F1e2b9f07/dea157afd2d9aa60.png" alt="" class="rightBtn" @click="ShowToast" v-if="prizeImgList?.length > 3"/>
    </div>
    <div class="twoDivClass" :style="furnishStyles.blindBoxBg.value">
      <div class="prizeClass">
        <div class="prizeItemClass" v-for="(item, index) in prizeListAll" :key="index">
          <img class="boxPrizes" v-if="item.isSelect" :src="item.lightLogoImg" alt=""/>
          <img class="boxPrizes" v-else :src="item.logoImg" alt=""/>
          <div class="chooseBtn" :style="chooseMeBtnStatus === 0 ? furnishStyles.drawBtn.value : furnishStyles.grayDrawBtn.value" @click.stop="selectMhClick(item, index)"></div>
        </div>
      </div>
      <div class="numText" :style="furnishStyles.prizeRemainNum.value">
        剩余数量：<span>{{ totalStock > 0 ? totalStock : 0 }}</span>（礼品总数）
      </div>
    </div>
    <div class="winners" :style="furnishStyles.winnerBg.value">
      <div class="winners-content">
        <div class="winner-list">
          <VueDanmaku v-if="activityGiftRecords?.length !== 0" ref="danmaku" v-model:danmus="activityGiftRecords" useSlot loop :channels="2" :speeds="100" class="danmaku">
            <template v-slot:dm="{ danmu }">
              <div class="winner" :style="furnishStyles.winnersDanMu.value">
                <span>恭喜{{ danmu.nickName }}抽中了{{ danmu.prizeName }}</span>
              </div>
            </template>
          </VueDanmaku>
          <div v-else>
            <p class="winner-null">暂无相关获奖信息哦~</p>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 规则弹窗 -->
  <VanPopup teleport="body" v-model:show="showRule" position="bottom">
    <RulePopup :rule="ruleTest" @close="showRule = false"></RulePopup>
  </VanPopup>
  <!-- 我的奖品弹窗 -->
  <VanPopup teleport="body" v-model:show="showMyPrize" position="bottom">
    <MyPrize @close="showMyPrize = false" v-if="showMyPrize"></MyPrize>
  </VanPopup>
  <!-- 中奖弹窗 -->
  <VanPopup teleport="body" v-model:show="showAward">
    <AwardPopup :prize="award" @close="showAward = false" @saveAddress="toSaveAddress"></AwardPopup>
  </VanPopup>
  <!-- 保存地址弹窗 -->
  <VanPopup teleport="body" v-model:show="showSaveAddress" position="center">
    <SaveAddress v-if="showSaveAddress" :addressId="addressId" :activityPrizeId="activityPrizeId" @close="showSaveAddress = false"></SaveAddress>
  </VanPopup>
  <!-- 活动已结束弹窗 -->
  <VanPopup teleport="body" v-model:show="showEnd" position="center">
    <ActEndPopup @close="showEnd = false"></ActEndPopup>
  </VanPopup>
  <!-- 盲盒确认选择弹窗 -->
  <VanPopup teleport="body" v-model:show="showChoose" position="center">
    <ChoosePopup @confirmChoose="confirmChooseBlind" @close="showChoose = false"></ChoosePopup>
  </VanPopup>
  <!-- 开卡弹窗 -->
  <VanPopup teleport="body" v-model:show="joinPopup">
    <OpenCardPopup @close="joinPopup = false"></OpenCardPopup>
  </VanPopup>
</template>

<script lang="ts" setup>
import furnishStyles, { furnish } from './ts/furnishStyles';
import { ref, reactive, inject } from 'vue';
import { closeToast, showLoadingToast, showToast } from 'vant';

import MyPrize from "./components/MyPrize.vue";
import RulePopup from "./components/RulePopup.vue";
import SaveAddress from "./components/SaveAddress.vue";
import AwardPopup from "./components/AwardPopup.vue";
import ActEndPopup from "./components/ActEndPopup.vue";
import ChoosePopup from "./components/ChoosePopup.vue";
import OpenCardPopup from "./components/OpenCardPopup.vue";

import VueDanmaku from "vue3-danmaku";
import { httpRequest } from '@/utils/service';
import {prizeListAll} from "./ts/type";
import {gotoShopPage} from "@/utils/platforms/jump";
import useThreshold from '@/hooks/useThreshold';

import Swiper, { Autoplay } from 'swiper';
import { DecoData } from '@/types/DecoData';
import { BaseInfo } from '@/types/BaseInfo';
import dayjs from 'dayjs';

Swiper.use([Autoplay]);

const decoData = inject('decoData') as DecoData;
const baseInfo = inject('baseInfo') as BaseInfo;

// 规则弹窗
const showRule = ref(false);
// 我的奖品弹窗
const showMyPrize = ref(false);
// 入会开卡弹窗
const joinPopup = ref(false);
// 活动已结束弹窗
const showEnd = ref(false);
// 选择盲盒弹窗
const showChoose = ref(false);
// 中奖弹窗
const showAward = ref(false);
// 保存实物地址弹窗
const showSaveAddress = ref(false);

// 抽奖次数
const chanceNum = ref(0);
// 选我按钮 状态 0-可以领取 1-没有订单 2-sku不符合 3-金额不符合 4-笔数不符合 5-抽奖次数不足 6-奖品
const chooseMeBtnStatus = ref(1);
// 奖品剩余库存
const totalStock = ref(0);
// 0-全部商品 1-指定商品  2-排除
const orderSkuisExposure = ref(0);
// 奖品图片列表(B端配置的小图)
const prizeImgList = ref([]);
const isSelectBlindBox = ref(false);
const isSelectBlindBoxIndex = ref(-1);

const ruleTest = ref('');
// 展示活动规则，首次获取规则
const showRulePopup = async () => {
  try {
    if (!ruleTest.value) {
      const { data } = await httpRequest.get('/common/getRule');
      ruleTest.value = data;
    }
    showRule.value = true;
  } catch (error) {
    console.error();
  }
};

// 选择盲盒
const selectMhClick = (item:any, index:number) => {
  if (baseInfo.thresholdResponseList && baseInfo.thresholdResponseList?.length) {
    showToast(baseInfo.thresholdResponseList[0].thresholdContent);
    return;
  }
  for (let i = 0; i < prizeListAll.length; i++) {
    prizeListAll[i].isSelect = false;
  }
  prizeListAll[index].isSelect = true;
  isSelectBlindBox.value = true;
  isSelectBlindBoxIndex.value = index + 1;
  showChoose.value = true;
};

// 不在活动范围呢刷新页面
const unStart = (partakeStartTime: string) => {
  const now = dayjs().format('YYYY-MM-DD');
  let time = 0;
  if (partakeStartTime > dayjs().format('HH:mm:ss')) {
    time = dayjs(`${now} ${partakeStartTime}`).valueOf() - dayjs().valueOf();
  } else {
    time = dayjs(`${now} ${partakeStartTime}`).add(1, 'day').valueOf() - dayjs().valueOf();
  }
  setTimeout(() => {
    window.location.reload();
  }, time);
};
// 获取页面数据-主接口
const getActInfo = async () => {
  try {
    const { data } = await httpRequest.post('/96010/chanceNum');
    prizeImgList.value = data.topPrizeImg;
    chanceNum.value = data.chanceNum;
    chooseMeBtnStatus.value = data.buttonStatus;
    totalStock.value = data.totalStock;
    orderSkuisExposure.value = data.orderSkuisExposure;
    if (baseInfo.thresholdResponseList && baseInfo.thresholdResponseList?.length) {
      const threshold = baseInfo.thresholdResponseList.find((item: any) => item.thresholdCode === 201);
      if (threshold) {
        unStart(data.partakeStartTime);
      }
    }
  } catch (error) {
    console.error(error);
  }
};

// 中奖名单泛型
interface ActivityGiftRecord {
  avatar: string;
  nickName: string;
  prizeName: string;
}
const activityGiftRecords = reactive([] as ActivityGiftRecord[]);
// 获取中奖名单
const getWinnerList = async () => {
  try {
    const res = await httpRequest.post('/96010/winners');
    activityGiftRecords.splice(0);
    activityGiftRecords.push(...res.data);
  } catch (error) {
    console.error(error);
  }
};

// 中奖奖品信息
const award = ref({
  prizeType: 0,
  prizeName: '',
  prizeImg: '',
  result: {
    result: {
      planDesc: '',
    },
  },
  activityPrizeId: '',
  userPrizeId: '',
});

// 开始抽奖
const startPlay = async () => {
  if (baseInfo.thresholdResponseList && baseInfo.thresholdResponseList?.length) {
    showToast(baseInfo.thresholdResponseList[0].thresholdContent);
    return;
  }
  if (!isSelectBlindBox.value) {
    showToast('请先选择心仪盲盒');
    return;
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    const { data } = await httpRequest.post('/96010/lotteryDraw');
    award.value = data;
    showAward.value = true;
    closeToast();
    await getWinnerList();
  } catch (error) {
    console.error(error);
    showToast(error.message);
  }
};

// 确认选择盲盒的回调
const confirmChooseBlind = async () => {
  showChoose.value = false;
  await startPlay();
};

const activityPrizeId = ref('');
const addressId = ref('');
const toSaveAddress = (id: string, prizeId: string) => {
  addressId.value = id;
  activityPrizeId.value = prizeId;
  showAward.value = false;
  showSaveAddress.value = true;
};

const init = async () => {
  if (decoData) {
    Object.keys(decoData).forEach((item) => {
      furnish[item] = decoData[item];
    });
  }
  try {
    showLoadingToast({
      message: '加载中...',
      forbidClick: true,
      duration: 0,
    });
    await Promise.all([getActInfo(), getWinnerList()]);
    closeToast();
  } catch (error) {
    closeToast();
  }
};
init();
</script>

<style scoped lang="scss">
.bg {
  width: 7.5rem;
  background-size: 100%;
  min-height: 100vh;
  background-repeat: no-repeat;
  position: relative;
  padding: 3.5rem 0 0;
}
.header-content {
  position: absolute;
  top: 1rem;
  right: 0;
  width: 1.13rem;
}
.header-btn {
  background-repeat: no-repeat;
  background-size: 100%;
  width: 1.13rem;
  height: 0.51rem;
  font-size: 0.2rem;
  line-height: 0.4rem;
  padding: 0.04rem 0 0.04rem 0.1rem;
  margin-bottom: 0.1rem;
  text-align: center;
  cursor: pointer;
}

.prizeBox{
  width: 4.8rem;
  height: 1.34rem;
  margin: 0 auto;
  display: flex;
  align-items: center;
  z-index: 999;
  .leftBtn{
    width: 0.16rem;
    height: 0.29rem;
    cursor: pointer;
  }
  .prizeListClass{
    flex: 1;
    display: flex;
    overflow: hidden;
    margin: 0 0.1rem;
    align-items: center;
    justify-content: left;
    .prizeItem{
      width: 1.3rem;
      height: 1.3rem;
      background: url("https://img10.360buyimg.com/imgzone/jfs/t1/341681/16/1024/4936/68bea3b9F449fae12/c1b845ce84578a0e.png") no-repeat;
      background-size: 100%;
      display: flex;
      align-items: center;
      margin: 0 0.06rem;
      flex-shrink: 0;
      img{
        width: 0.8rem;
        height: 0.8rem;
        margin: 0 auto;
      }
    }
  }
  .rightBtn{
    width: 0.16rem;
    height: 0.29rem;
    cursor: pointer;
  }
}

.twoDivClass{
  width: 7.5rem;
  height: 11rem;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-top: -0.8rem;
  position: relative;
  .numText {
    position: absolute;
    font-size: 0.2rem;
    bottom: 0.45rem;
  }
  .prizeClass{
    width: 5.3rem;
    height: 5rem;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    margin-top: 2.9rem;
    .prizeItemClass{
      flex: 1.73rem;
      margin: 0 auto 0.23rem;
      .selectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/237000/27/5276/2892/65697800F1cd69330/3d3d413da692bff5.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 1.5rem;
        height: 0.49rem;
      }
      .noSelectClass{
        background-image: url("//img10.360buyimg.com/imgzone/jfs/t1/224061/3/5097/2766/65697800F1c398617/4b73537eb60825b6.png");
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width:1.5rem;
        height: 0.49rem;
      }
      .lightBoxPrizes{
        width: 1.33rem;
        height: 1.68rem;
        margin: 0 auto;
      }
      .boxPrizes {
        width: 1.25rem;
        height: 1.6rem;
        margin: 0 auto;
      }
      .chooseBtn{
        background-size: 100%;
        background-repeat: no-repeat;
        width: 1.01rem;
        height: 0.36rem;
        margin: -0.1rem auto 0;
        z-index: 10;
        position: relative;
      }
    }
  }

}

.winners {
  background-size: 100%;
  background-repeat: no-repeat;
  width: 7.5rem;
  height: 3.26rem;
  margin: 0 auto;

  .winners-content {
    width: 7.5rem;
    height: 3.26rem;
    margin: 0 auto;
    overflow: hidden;
    padding-top: 0.8rem;
  }
  .danmaku {
    width: 7.5rem;
    height: 3rem;
    margin: 0 auto;
  }
}

.winner-list {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.winner {
  height: 0.83rem;
  width: 4.55rem;
  background-repeat: no-repeat;
  background-size: 100%;
  padding-left: 0.9rem;
  padding-right: 0.4rem;
  color: #f5e4bd;
  font-size: 0.2rem;
  text-align: center;
  line-height: 0.92rem;
  margin: 0.1rem 0 0 0.5rem;
}

.winner-null {
  text-align: center;
  line-height: 2.3rem;
  font-size: 0.24rem;
  color: #f5e4bd;
}

</style>
<style>
*::-webkit-scrollbar {
  width: 0 !important;
  display: none !important;
}
</style>
