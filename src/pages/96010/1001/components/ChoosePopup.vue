<template>
  <div class="choose-bk">
    <div class="close" @click="close"/>
    <div class="blind-gif">
      <img :src="decoData.blindGif || furnish.blindGif" alt="">
    </div>
    <img class="text-img" src="//img10.360buyimg.com/imgzone/jfs/t1/324129/1/18447/3474/68c120c6F2f011208/a9ce2a34bc8007a7.png" alt="">
    <div class="btn-list">
      <div class="chanel" @click="close"/>
      <div class="confirm" @click="confirmChoose" v-click-track="'qrdk'"/>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {inject} from "vue";
import { furnish } from '../ts/furnishStyles';
import {DecoData} from "@/types/DecoData";
const emits = defineEmits(['close', 'confirmChoose']);

const decoData = inject('decoData') as DecoData;

const close = () => {
  emits('close');
};

const confirmChoose = () => {
  emits('confirmChoose');
};
</script>

<style scoped lang="scss">
.choose-bk {
  width: 5.82rem;
  height: 8rem;
  position: relative;
  padding-top: 0.1rem;
  .close {
    height: 0.6rem;
    width: 0.6rem;
    position: absolute;
    right: 0;
    top: 0;
    background: url(https://img10.360buyimg.com/imgzone/jfs/t1/329905/11/11618/1985/68c0e58fFeba0bd80/44949676a2c2b335.png) no-repeat;
    background-size: 100% 100%;
  }
  .blind-gif{
    width: 5.7rem;
    height: 5.95rem;
    margin: 0 auto;
    img{
      width: 100%;
      height: 100%;
    }
  }
  .text-img{
    width: 3.49rem;
    margin: 0 auto 0.5rem;
  }
  .btn-list{
    display: flex;
    justify-content: space-between;
    width: 5rem;
    margin: 0 auto;
    .chanel{
      width: 2.1rem;
      height: 0.85rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/337535/33/9248/3658/68c122b0F81dfb997/6658513214adcc23.png) no-repeat;
      background-size: 100%;
    }
    .confirm{
      width: 2.1rem;
      height: 0.85rem;
      background: url(https://img10.360buyimg.com/imgzone/jfs/t1/349421/7/1765/3857/68c122afFa884e7b9/f06a6f9c93646ee1.png) no-repeat;
      background-size: 100%;
    }
  }
}
</style>
