<template>
  <img v-if="imgLoaded" :src="imgUrl" :style="styleObject" @click="$emit('click')" crossorigin="anonymous" />
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps<{
  imgUrl: any;
  scale?: number;
}>();

const emit = defineEmits(['click']);

const scale = props.scale || 1;
const imgLoaded = ref(false);
const styleObject = ref<Record<string, string>>({});

watch(
  () => props.imgUrl,
  (newUrl) => {
    if (!newUrl) return;
    imgLoaded.value = false;
    const img = new Image();
    img.src = newUrl;
    img.onload = () => {
      const originalWidth = img.width;
      const originalHeight = img.height;
      const widthRem = originalWidth / scale / 100;
      const heightRem = originalHeight / scale / 100;
      styleObject.value = {
        width: `${widthRem}rem`,
        height: `${heightRem}rem`,
        maxWidth: 'max-content',
      };
      imgLoaded.value = true;
    };
  },
  { immediate: true },
);
</script>
