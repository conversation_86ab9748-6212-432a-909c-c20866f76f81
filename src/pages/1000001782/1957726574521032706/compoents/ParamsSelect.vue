<template>
  <VanPopup v-model:show="show" :closeable="false" :style="{ height: '100%', width: '100%' }" @open="openPopup">
    <van-tabs v-model:active="paramsActive" shrink @change="handleTabChange" ref="tabsRef">
      <div style="overflow-x: auto">
        <!-- 所有tabs统一渲染 -->
        <van-tab v-for="(tab, index) in tabs" :key="tab.name">
          <template #title>
            <img v-if="tab.name === 'hot'" style="width: 0.3rem; object-fit: contain" src="//img10.360buyimg.com/imgzone/jfs/t1/322640/19/15764/2170/68be4aaaF67adeef1/86680196d4e53a5d.png" alt="" />
            <img v-if="tab.name === 'golden'" style="width: 0.2rem; object-fit: contain" src="//img10.360buyimg.com/imgzone/jfs/t1/327797/4/17190/1289/68be4aaaFe4cac195/98b5b9257633c6dc.png" alt="" />
            <img v-if="tab.name === 'cart'" style="width: 0.3rem; object-fit: contain" src="//img10.360buyimg.com/imgzone/jfs/t1/288481/9/19551/1029/68b513cdF10063364/854f4299120cbae3.png" alt="" />
            <span style="margin-left: 4px">{{ tab.title }}</span>
          </template>

          <!-- 热门推荐tab内容 -->
          <div v-if="tab.name === 'hot'" class="product-list">
            <div class="search">
              <van-search v-model="value" show-action placeholder="请输入关键字" @search="onSearch">
                <template #action>
                  <div @click="onClickButton" style="color: #2559f6; font-weight: bold">搜索</div>
                </template>
              </van-search>
            </div>
            <van-checkbox-group v-model="selectedHotProducts" shape="square">
              <div v-for="(item, idx) in hotList" :key="idx" class="product-item">
                <van-checkbox :name="item.id" :disabled="selectedHotProducts.length >= maxSelectableCount && !selectedHotProducts.includes(item.id)" class="product-checkbox" label-disabled>
                  <div class="product-content" @click="gotoSkuPage(item.skuId)">
                    <img :src="item.skuImage" alt="" class="product-image" />
                    <div class="product-info">
                      <div class="product-title">{{ item.skuName }}</div>
                      <div class="product-price">¥ {{ item.skuPrice }}</div>
                    </div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>

          <!-- 店铺金榜tab内容 -->
          <div v-if="tab.name === 'golden'" class="product-list">
            <div class="search">
              <van-search v-model="value" show-action placeholder="请输入关键字" @search="onSearch">
                <template #action>
                  <div @click="onClickButton" style="color: #2559f6; font-weight: bold">搜索</div>
                </template>
              </van-search>
            </div>
            <van-checkbox-group v-model="selectedGoldenProducts" shape="square">
              <div v-for="(item, idx) in goldenList" :key="idx" class="product-item">
                <van-checkbox :name="item.id" :disabled="selectedGoldenProducts.length >= maxSelectableCount && !selectedGoldenProducts.includes(item.id)" class="product-checkbox" label-disabled>
                  <div class="product-content" @click="gotoSkuPage(item.skuId)">
                    <img :src="item.skuImage" alt="" class="product-image" />
                    <div class="product-info">
                      <div class="product-title">{{ item.skuName }}</div>
                      <div class="product-price">¥ {{ item.skuPrice }}</div>
                    </div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>

          <!-- 购物车tab内容 -->
          <div v-if="tab.name === 'cart'" class="product-list">
            <div class="search">
              <van-search v-model="value" show-action placeholder="请输入关键字" @search="onSearch">
                <template #action>
                  <div @click="onClickButton" style="color: #24193e; font-weight: bold">搜索</div>
                </template>
              </van-search>
            </div>
            <van-checkbox-group v-model="selectedCartProducts" shape="square">
              <div v-for="(item, idx) in cartList" :key="idx" class="product-item">
                <van-checkbox :name="item.id" :disabled="selectedCartProducts.length >= maxSelectableCount && !selectedCartProducts.includes(item.id)" class="product-checkbox" label-disabled>
                  <div class="product-content" @click="gotoSkuPage(item.skuId)">
                    <img :src="item.skuImage" alt="" class="product-image" />
                    <div class="product-info">
                      <div class="product-title">{{ item.skuName }}</div>
                      <div class="product-price">¥ {{ item.skuPrice }}</div>
                    </div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>
          <!-- 筛选商品展示界面 -->
          <div class="product-list" v-if="!tab.data && tab.name !== 'hot' && tab.name !== 'golden' && tab.name !== 'cart'">
            <!-- <div class="search">
              <van-search v-model="value" show-action placeholder="请输入关键字" @search="onSearch">
                <template #action>
                  <div @click="onClickButton" style="color: #1a60f5; font-weight: bold">搜索</div>
                </template>
              </van-search>
            </div> -->
            <van-checkbox-group v-model="selectedProducts" shape="square">
              <div v-for="(item, idx) in productList" :key="idx" class="product-item">
                <van-checkbox :name="item.id" :disabled="selectedProducts.length >= maxSelectableCount && !selectedProducts.includes(item.id)" class="product-checkbox" label-disabled>
                  <div class="product-content" @click="gotoSkuPage(item.skuId)">
                    <img :src="item.skuImage" alt="" class="product-image" />
                    <div class="product-info">
                      <div class="product-title">{{ item.skuName }}</div>
                      <div class="product-price">¥ {{ item.skuPrice }}</div>
                    </div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>

          <!-- 规格选择界面 -->
          <div class="product-list" v-else-if="tab.data && !tab.data.lastSpecifications">
            <div class="product-item spec-item all-item">
              <van-checkbox :model-value="isAllSelected(tab.data)" @click="handleSelectAll(index)" class="product-checkbox" shape="square" label-disabled>
                <div class="spec-content">
                  <div class="spec-value">{{ isAllSelected(tab.data) ? '取消全选' : '全选' }}</div>
                </div>
              </van-checkbox>
            </div>
            <van-checkbox-group :model-value="tab.data.selectedValues" @update:model-value="(values) => handleSpecificationChange(index, values)" shape="square">
              <div v-for="option in tab.data.valueList" :key="option.id" class="product-item spec-item">
                <van-checkbox :name="option.id" class="product-checkbox" label-disabled>
                  <div class="spec-content">
                    <div class="spec-value">{{ option.value }}</div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>

          <!-- 最后一级规格选择界面 -->
          <div class="product-list" v-else-if="tab.data && tab.data.lastSpecifications">
            <div class="product-item spec-item all-item">
              <van-checkbox :model-value="isAllSelected(tab.data)" @click="handleSelectAll(index)" class="product-checkbox" shape="square" label-disabled>
                <div class="spec-content">
                  <div class="spec-value">{{ isAllSelected(tab.data) ? '取消全选' : '全选' }}</div>
                </div>
              </van-checkbox>
            </div>
            <van-checkbox-group :model-value="tab.data.selectedValues" @update:model-value="(values) => handleSpecificationChange(index, values)" shape="square">
              <div v-for="option in tab.data.valueList" :key="option.id" class="product-item spec-item">
                <van-checkbox :name="option.id" class="product-checkbox" label-disabled>
                  <div class="spec-content">
                    <div class="spec-value">{{ option.value }}</div>
                  </div>
                </van-checkbox>
              </div>
            </van-checkbox-group>
          </div>
          <div class="spec-bottom" v-if="!tab.data || tab.name === 'hot' || tab.name === 'cart'">温馨提示：最多选择2条数据</div>
        </van-tab>
      </div>
      <!-- 热门商品tab -->
    </van-tabs>
    <!-- 底部对比按钮 -->
    <div class="bottom-actions">
      <van-button v-if="isButtonDisabled" class="compare-btn-disabled" disabled> {{ buttonText }} </van-button>
      <van-button v-else class="compare-btn" @click="handleCompare"> {{ buttonText }} </van-button>
    </div>
  </VanPopup>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, computed, onMounted, nextTick } from 'vue';
import { showToast } from 'vant';
import { getSkuDetailHotList, getSpecifications, getSkuInfoList, getSkuDetailCartList, getGoldenSkuInfos } from '../ajax/ajax';
import { gotoSkuPage } from '@/utils/platforms/jump';

// 获取SKU商品列表
const getSkuInfoListData = async (params: { categoryId: string; specificationsIds: string[] }) => {
  try {
    return await getSkuInfoList(params);
  } catch (error) {
    console.error('获取SKU商品列表失败:', error);
    throw error;
  }
};

// 获取购物车商品列表
const getSkuDetailCartListData = async (categoryId: string) => {
  try {
    return await getSkuDetailCartList(categoryId);
  } catch (error) {
    console.error('获取SKU商品列表失败:', error);
    throw error;
  }
};
// 定义商品数据类型
interface Product {
  id: string;
  skuId: string;
  skuName: string;
  skuPrice: string;
  skuImage: string;
}

// 定义规格选项数据类型
interface SpecificationValue {
  id: string;
  value: string;
}

// 定义规格数据类型
interface SpecificationData {
  id: string;
  specificationsName: string;
  valueList: SpecificationValue[];
  lastSpecifications: boolean;
  selectedValues: string[]; // 当前tab选中的值
}

// 定义tab项类型
interface TabItem {
  name: string;
  title: string;
  data?: SpecificationData;
}

// 定义规格选项数据类型
interface SpecificationValue {
  id: string;
  value: string;
}

// 定义tab数据类型
interface TabData {
  id: string;
  title: string;
  valueList: SpecificationValue[];
  selectedValues: string[];
  lastSpecifications: boolean;
}

// 定义接口返回数据类型
interface SpecificationResponse {
  id: string;
  lastSpecifications: boolean;
  specificationsName: string;
  valueList: SpecificationValue[];
}

const props = defineProps({
  showPopup: {
    type: Boolean,
    default: false,
  },
  categoryId: {
    type: String,
    default: '0',
  },
  currentDataCount: {
    type: Number,
    default: 0,
  },
});
const paramsActive = ref(0);
const emits = defineEmits(['closePopup', 'compareProducts']);
const show = computed(() => props.showPopup);
const hotList = ref<Product[]>([]);
const goldenList = ref<Product[]>([]); // 店铺金榜商品列表
const cartList = ref<Product[]>([]); // 购物车商品列表
const selectedProducts = ref<string[]>([]); // 筛选商品tab的选择状态
const selectedHotProducts = ref<string[]>([]); // 热门商品tab的选择状态
const selectedGoldenProducts = ref<string[]>([]); // 店铺金榜tab的选择状态
const selectedCartProducts = ref<string[]>([]); // 购物车tab的选择状态
const specificationsId = ref('0');
const valueIds = ref([]);
const value = ref(''); // 搜索关键词
const productList = ref<Product[]>([]); // 规格查询后的商品列表

// 动态tab相关状态
const tabs = ref<TabItem[]>([
  { name: 'hot', title: '人气新品' },
  { name: 'golden', title: '店铺金榜' },
  { name: 'cart', title: '购物车' },
]);
const specificationTabs = ref<SpecificationData[]>([]); // 规格tab数据缓存
const currentSpecLevel = ref(0); // 当前规格级别

// 计算可添加的商品数量（主页最多4条数据）
const maxSelectableCount = computed(() => {
  return Math.max(0, 4 - props.currentDataCount);
});

// 计算提示文案
const tipText = computed(() => {
  const currentTab = tabs.value[paramsActive.value];

  // 如果是规格选择tab
  if (currentTab.data && !currentTab.name.startsWith('product-')) {
    return '温馨提示：最多选择3条数据';
  }

  // 如果是商品选择tab（热门商品或筛选商品）
  return '温馨提示：最多选择2条数据';
});
// 获取热门推荐商品列表（已替换为带备份功能的版本）
// 获取规格信息
const getSpecificationsInfo = async (specId: string = '0', values: string[] = []) => {
  try {
    // 获取所有父级规格信息，第一次查询时为空数组
    const specificationsInfos = specId === '0' ? [] : getCurrentSpecificationPath();

    const params = {
      categoryId: props.categoryId,
      // specificationsId: specId,
      // valueIds: values,
      specificationsInfos: specificationsInfos,
    };
    const data = await getSpecifications(params);

    if (data && data.specificationsName) {
      // 创建新的规格数据
      const newSpecData: SpecificationData = {
        id: data.id,
        specificationsName: data.specificationsName,
        valueList: data.valueList || [],
        lastSpecifications: data.lastSpecifications,
        selectedValues: [],
      };

      // 添加到规格tabs缓存
      addSpecificationTab(newSpecData);
    }

    return data;
  } catch (error) {
    console.log(error);
    return null;
  }
};

// 添加规格tab
const addSpecificationTab = (specData: SpecificationData) => {
  const level = currentSpecLevel.value;

  // 如果只有一个规格选项，自动选中
  if (specData.valueList.length === 1) {
    specData.selectedValues = [specData.valueList[0].id];
  }

  // 如果当前级别已存在，替换数据
  if (specificationTabs.value[level]) {
    specificationTabs.value[level] = specData;
  } else {
    specificationTabs.value.push(specData);
  }

  // 更新tabs数组
  updateTabsArray();

  // 如果只有一个规格选项，自动跳转到下一级
  if (specData.valueList.length === 1) {
    nextTick(() => {
      handleAutoNextLevel(specData);
    });
  }
};

// 更新tabs数组
const updateTabsArray = () => {
  // 保留固定的三个tab（热门推荐、店铺金榜和购物车）
  const fixedTabs = tabs.value.slice(0, 3);
  const newTabs: TabItem[] = [...fixedTabs];

  specificationTabs.value.forEach((spec, index) => {
    let title = spec.specificationsName;

    // 如果有选中的值，将选中值用英文逗号连接显示在标题中
    if (spec.selectedValues.length > 0) {
      const selectedLabels = spec.selectedValues.map((valueId) => {
        const option = spec.valueList.find((item) => item.id === valueId);
        return option ? option.value : valueId;
      });
      title = `${selectedLabels.join(', ')}`;
    }

    newTabs.push({
      name: `spec_${index}`,
      title: title,
      data: spec,
    });
  });

  tabs.value = newTabs;
};

// 删除指定级别后的所有tab
const removeTabsAfterLevel = (level: number) => {
  specificationTabs.value = specificationTabs.value.slice(0, level + 1);
  updateTabsArray();
};
const openPopup = async () => {
  // 重置状态
  paramsActive.value = 0;
  currentSpecLevel.value = 0;
  specificationTabs.value = [];
  tabs.value = [
    { name: 'golden', title: '店铺金榜' },
    { name: 'hot', title: '人气新品' },
    { name: 'cart', title: '购物车' },
  ];
  selectedProducts.value = [];
  selectedHotProducts.value = [];
  selectedGoldenProducts.value = [];
  selectedCartProducts.value = [];
  // 获取第一级规格数据
  await getSpecificationsInfo('0', []);
  // 获取热门商品数据
  await getHotSkuListWithBackup();
  // 获取店铺金榜商品数据
  await getGoldenSkuListWithBackup();
  // 获取购物车商品数据
  await getCartSkuListWithBackup();
};

// 获取购物车商品列表
const getCartSkuListWithBackup = async () => {
  try {
    cartList.value = await getSkuDetailCartListData(props.categoryId);
    // 保存原始购物车商品列表
    originalCartList.value = [...cartList.value];
    console.log('购物车商品列表:', cartList.value);
  } catch (error) {
    console.log('获取购物车商品失败:', error);
  }
};

// 获取当前选中的规格路径
const getCurrentSpecificationPath = () => {
  const path: Array<{ specificationsId: string; valueIds: string[] }> = [];

  for (let i = 0; i <= currentSpecLevel.value && i < specificationTabs.value.length; i++) {
    const spec = specificationTabs.value[i];
    if (spec.selectedValues.length > 0) {
      path.push({
        specificationsId: spec.id,
        valueIds: spec.selectedValues,
      });
    }
  }

  return path;
};

const handleCompare = async () => {
  const currentTab = tabs.value[paramsActive.value];

  // 如果是热门商品tab
  if (currentTab.name === 'hot') {
    if (selectedHotProducts.value.length === 0) {
      console.log('请选择商品进行对比');
      return;
    }
    if (selectedHotProducts.value.length > maxSelectableCount.value) {
      console.log(`最多只能选择${maxSelectableCount.value}个商品进行对比`);
      return;
    }
    // 执行商品对比逻辑
    console.log('开始对比商品:', selectedHotProducts.value);
    emits('compareProducts', selectedHotProducts.value);
    return;
  }

  // 如果是店铺金榜tab
  if (currentTab.name === 'golden') {
    if (selectedGoldenProducts.value.length === 0) {
      console.log('请选择商品进行对比');
      return;
    }
    if (selectedGoldenProducts.value.length > maxSelectableCount.value) {
      console.log(`最多只能选择${maxSelectableCount.value}个商品进行对比`);
      return;
    }
    // 执行商品对比逻辑
    console.log('开始对比店铺金榜商品:', selectedGoldenProducts.value);
    emits('compareProducts', selectedGoldenProducts.value);
    return;
  }

  // 如果是购物车tab
  if (currentTab.name === 'cart') {
    if (selectedCartProducts.value.length === 0) {
      console.log('请选择商品进行对比');
      return;
    }
    if (selectedCartProducts.value.length > maxSelectableCount.value) {
      console.log(`最多只能选择${maxSelectableCount.value}个商品进行对比`);
      return;
    }
    // 执行商品对比逻辑
    console.log('开始对比购物车商品:', selectedCartProducts.value);
    emits('compareProducts', selectedCartProducts.value);
    return;
  }

  // 如果是商品tab（筛选商品展示界面）
  if (currentTab.name && currentTab.name.startsWith('product-')) {
    const selectedCount = selectedProducts.value.length;
    if (selectedCount < 1) {
      showToast('请至少选择1个商品进行对比');
      return;
    }
    if (selectedCount > maxSelectableCount.value) {
      showToast(`最多只能选择${maxSelectableCount.value}个商品进行对比`);
      return;
    }
    // 执行对比逻辑
    console.log('开始对比商品:', selectedProducts.value);
    emits('compareProducts', selectedProducts.value);
    return;
  }

  // 如果是规格tab
  if (currentTab.data) {
    const specData = currentTab.data;

    if (specData.selectedValues.length === 0) {
      console.log('请选择规格选项');
      return;
    }

    // 如果是最后一级规格
    if (specData.lastSpecifications) {
      // 查询商品数据并创建商品tab
      const allSelectedSpecs = getAllSelectedSpecifications();
      // 获取所有选中的规格值的id
      const specificationsIds = allSelectedSpecs.reduce((acc, spec) => {
        return acc.concat(spec.selectedValues);
      }, [] as string[]);
      try {
        const res = await getSkuInfoListData({
          categoryId: props.categoryId,
          specificationsIds: specificationsIds,
        });
        // 更新商品列表
        productList.value = res || [];
        // 备份原始商品列表
        originalProductList.value = [...productList.value];
        // 清空筛选商品tab的选择状态
        selectedProducts.value = [];

        // 创建一个新的商品展示tab，标题固定为"商品"
        const productTab = {
          name: `product-${Date.now()}`,
          title: '商品',
          data: undefined,
        };
        tabs.value.push(productTab);
        // 切换到新创建的商品tab
        const newTabIndex = tabs.value.length - 1;
        // 使用nextTick确保DOM更新后再切换tab
        await nextTick();
        paramsActive.value = newTabIndex;
      } catch (err) {
        showToast('查询商品失败，请重试1');
      }
      return;
    }

    // 如果不是最后一级，查询下一级数据
    currentSpecLevel.value++;
    await getSpecificationsInfo(specData.id, specData.selectedValues);

    // 切换到新创建的tab
    paramsActive.value = tabs.value.length - 1;
  }
};

// 获取所有已选择的规格
const getAllSelectedSpecifications = () => {
  return specificationTabs.value.map((spec) => ({
    id: spec.id,
    name: spec.specificationsName,
    selectedValues: spec.selectedValues,
  }));
};

// 处理tab切换
const handleTabChange = async (index: number) => {
  console.log('🚀 ~ handleTabChange ~ index:', index, 'tabs.length:', tabs.value.length, 'currentTab:', tabs.value[index]?.title);
  const previousIndex = paramsActive.value;
  // 只有在实际需要切换时才更新paramsActive
  if (previousIndex !== index) {
    paramsActive.value = index;
  }

  const currentTab = tabs.value[index];
  if (!currentTab) return;

  // 如果切换到热门商品tab
  if (currentTab.name === 'hot') {
    currentSpecLevel.value = 0;
    // 清空热门商品tab的选择状态
    selectedHotProducts.value = [];
    // 重新调用热门商品接口
    await getHotSkuListWithBackup();
    return;
  }
  // 如果切换到店铺金榜tab
  if (currentTab.name === 'golden') {
    currentSpecLevel.value = 0;
    // 清空店铺金榜tab的选择状态
    selectedGoldenProducts.value = [];
    // 重新调用店铺金榜商品接口
    await getGoldenSkuListWithBackup();
    return;
  }
  // 如果切换到购物车tab
  if (currentTab.name === 'cart') {
    currentSpecLevel.value = 0;
    // 清空购物车tab的选择状态
    selectedCartProducts.value = [];
    // 重新调用购物车商品接口
    await getCartSkuListWithBackup();
    return;
  }
  // 检查是否切换到商品tab
  if (currentTab && currentTab.title === '商品') {
    // 重新调用getSkuInfoListData接口
    const allSelectedSpecs = getAllSelectedSpecifications();
    if (allSelectedSpecs.length > 0) {
      const specificationsIds = allSelectedSpecs.reduce<string[]>((acc, spec) => acc.concat(spec.selectedValues), []);
      try {
        const res = await getSkuInfoListData({
          categoryId: props.categoryId,
          specificationsIds,
        });
        productList.value = res || [];
        // 备份原始商品列表
        originalProductList.value = [...productList.value];
        // 只清空筛选商品tab的选择状态
        selectedProducts.value = [];
      } catch (err) {
        showToast('查询商品失败，请重试2');
      }
    }
    return;
  }

  // 如果切换到规格tab
  // 动态计算规格tab的起始索引（热门商品 + 店铺金榜 + 购物车tab的数量）
  const nonSpecTabCount = tabs.value.filter((tab) => tab.name === 'hot' || tab.name === 'golden' || tab.name === 'cart').length;
  const specIndex = index - nonSpecTabCount;

  // 检查是否是规格tab（通过tab名称判断）
  if (currentTab.name && currentTab.name.startsWith('spec-')) {
    // 如果切换到已存在的规格tab
    if (specIndex >= 0 && specIndex < specificationTabs.value.length) {
      currentSpecLevel.value = specIndex;

      // 如果用户从后面的tab切换到前面的tab，需要备份当前路径
      if (previousIndex > index) {
        const currentPath = getCurrentSpecificationPath();
        // 这里可以添加路径备份逻辑，用于后续恢复
        console.log('备份当前规格路径:', currentPath);
      }
    }
    return;
  }

  // 如果不是已知的tab类型，进行兜底处理
  if (!currentTab.name || (!currentTab.name.startsWith('spec-') && currentTab.name !== 'hot' && currentTab.name !== 'golden' && currentTab.name !== 'cart' && !currentTab.name.startsWith('product-'))) {
    // 重置到最后一个有效的规格tab
    const lastValidIndex = nonSpecTabCount + specificationTabs.value.length - 1;
    if (lastValidIndex >= nonSpecTabCount) {
      paramsActive.value = lastValidIndex;
      currentSpecLevel.value = specificationTabs.value.length - 1;
    }
  }
};

// 处理自动跳转下一级
const handleAutoNextLevel = async (specData: SpecificationData) => {
  if (specData.lastSpecifications) {
    // 如果是最后一级规格，查询商品数据
    const allSelectedSpecs = getAllSelectedSpecifications();
    const specificationsIds = allSelectedSpecs.reduce((acc, spec) => {
      return acc.concat(spec.selectedValues);
    }, [] as string[]);
    try {
      const res = await getSkuInfoListData({
        categoryId: props.categoryId,
        specificationsIds: specificationsIds,
      });
      productList.value = res || [];
      // 备份原始商品列表
      originalProductList.value = [...productList.value];
      selectedProducts.value = [];

      const productTab = {
        name: `product-${Date.now()}`,
        title: '商品',
        data: undefined,
      };
      tabs.value.push(productTab);
      const newTabIndex = tabs.value.length - 1;
      await nextTick();
      paramsActive.value = newTabIndex;
    } catch (err) {
      showToast('查询商品失败，请重试');
    }
  } else {
    // 如果不是最后一级，查询下一级数据
    currentSpecLevel.value++;
    await getSpecificationsInfo(specData.id, specData.selectedValues);
    paramsActive.value = tabs.value.length - 1;
  }
};

// 处理规格选择变化
const handleSpecificationChange = (tabIndex: number, selectedValues: string[]) => {
  // 动态计算规格tab的起始索引
  const nonSpecTabCount = tabs.value.filter((tab) => tab.name === 'hot' || tab.name === 'golden' || tab.name === 'cart').length;
  const specIndex = tabIndex - nonSpecTabCount;

  if (specIndex >= 0 && specificationTabs.value[specIndex]) {
    const oldValues = specificationTabs.value[specIndex].selectedValues;
    specificationTabs.value[specIndex].selectedValues = selectedValues;

    // 如果选择发生变化，删除后续所有tab
    if (JSON.stringify(oldValues) !== JSON.stringify(selectedValues)) {
      removeTabsAfterLevel(specIndex);
      currentSpecLevel.value = specIndex;
    }
  }
};

// 处理全选/取消全选
const handleSelectAll = (tabIndex: number) => {
  // 动态计算规格tab的起始索引
  const nonSpecTabCount = tabs.value.filter((tab) => tab.name === 'hot' || tab.name === 'golden' || tab.name === 'cart').length;
  const specIndex = tabIndex - nonSpecTabCount;

  if (specIndex >= 0 && specificationTabs.value[specIndex]) {
    const tabData = specificationTabs.value[specIndex];
    const allOptionIds = tabData.valueList.map((option) => option.id);

    // 如果当前是全选状态，则取消全选；否则全选
    if (isAllSelected(tabData)) {
      tabData.selectedValues = [];
    } else {
      tabData.selectedValues = [...allOptionIds];
    }

    // 删除后续所有tab
    removeTabsAfterLevel(specIndex);
    currentSpecLevel.value = specIndex;
  }
};

// 判断是否全选
const isAllSelected = (tabData: SpecificationData | TabData) => {
  if (!tabData || !tabData.valueList || tabData.valueList.length === 0) {
    return false;
  }
  return tabData.selectedValues.length === tabData.valueList.length;
};

// 计算当前按钮文本
const buttonText = computed(() => {
  const currentTab = tabs.value[paramsActive.value];

  // 如果没有可添加空间，显示提示
  if (maxSelectableCount.value === 0) {
    return '立即对比';
  }

  if (currentTab.name === 'hot') {
    return `立即对比`;
  }

  if (currentTab.name === 'cart') {
    return `立即对比`;
  }

  // 如果是商品tab（筛选商品展示界面）
  if (currentTab.name && currentTab.name.startsWith('product-')) {
    return `开始对比`;
  }

  // 如果是规格tab，按钮始终显示"确认"
  if (currentTab.data) {
    return '确认';
  }

  return '确认';
});

// 计算按钮是否禁用
const isButtonDisabled = computed(() => {
  const currentTab = tabs.value[paramsActive.value];

  // 如果没有可添加空间，禁用按钮
  if (maxSelectableCount.value === 0) {
    return true;
  }

  if (currentTab.name === 'hot') {
    return selectedHotProducts.value.length < 1;
  }

  if (currentTab.name === 'cart') {
    return selectedCartProducts.value.length < 1;
  }

  // 如果是商品tab（筛选商品展示界面）
  if (currentTab.name && currentTab.name.startsWith('product-')) {
    return selectedProducts.value.length < 1;
  }

  // 如果是规格tab，检查规格选择
  if (currentTab.data) {
    return currentTab.data.selectedValues.length === 0;
  }

  return true;
});

// 搜索功能
const onSearch = (val: string) => {
  console.log('搜索关键词:', val);
  performSearch(val);
};

const onClickButton = () => {
  console.log('点击搜索按钮，关键词:', value.value);
  performSearch(value.value);
};

// 存储原始商品列表，用于搜索过滤
const originalHotList = ref<Product[]>([]);
const originalGoldenList = ref<Product[]>([]);
const originalCartList = ref<Product[]>([]);
const originalProductList = ref<Product[]>([]);

const performSearch = (keyword: string) => {
  if (!keyword.trim()) {
    // 如果搜索关键词为空，恢复原始列表
    restoreOriginalLists();
    return;
  }

  const currentTab = tabs.value[paramsActive.value];

  // 如果是热门商品tab，搜索热门商品
  if (currentTab.name === 'hot') {
    console.log('在热门商品中搜索:', keyword);
    // 如果还没有保存原始列表，先保存
    if (originalHotList.value.length === 0 && hotList.value.length > 0) {
      originalHotList.value = [...hotList.value];
    }
    // 根据商品名称过滤热门商品列表
    hotList.value = originalHotList.value.filter((product) => product.skuName && product.skuName.toLowerCase().includes(keyword.toLowerCase()));
  }

  // 如果是店铺金榜tab，搜索店铺金榜商品
  if (currentTab.name === 'golden') {
    console.log('在店铺金榜中搜索:', keyword);
    // 如果还没有保存原始列表，先保存
    if (originalGoldenList.value.length === 0 && goldenList.value.length > 0) {
      originalGoldenList.value = [...goldenList.value];
    }
    // 根据商品名称过滤店铺金榜商品列表
    goldenList.value = originalGoldenList.value.filter((product) => product.skuName && product.skuName.toLowerCase().includes(keyword.toLowerCase()));
  }

  // 如果是购物车tab，搜索购物车商品
  if (currentTab.name === 'cart') {
    console.log('在购物车商品中搜索:', keyword);
    // 如果还没有保存原始列表，先保存
    if (originalCartList.value.length === 0 && cartList.value.length > 0) {
      originalCartList.value = [...cartList.value];
    }
    // 根据商品名称过滤购物车商品列表
    cartList.value = originalCartList.value.filter((product) => product.skuName && product.skuName.toLowerCase().includes(keyword.toLowerCase()));
  }

  // 如果是商品tab，搜索商品列表
  if (currentTab.name && currentTab.name.startsWith('product-')) {
    console.log('在商品列表中搜索:', keyword);
    // 如果还没有保存原始列表，先保存
    if (originalProductList.value.length === 0 && productList.value.length > 0) {
      originalProductList.value = [...productList.value];
    }
    // 根据商品名称过滤商品列表
    productList.value = originalProductList.value.filter((product) => product.skuName && product.skuName.toLowerCase().includes(keyword.toLowerCase()));
  }
};

// 恢复原始列表
const restoreOriginalLists = () => {
  const currentTab = tabs.value[paramsActive.value];

  if (currentTab.name === 'hot' && originalHotList.value.length > 0) {
    hotList.value = [...originalHotList.value];
  }

  if (currentTab.name === 'golden' && originalGoldenList.value.length > 0) {
    goldenList.value = [...originalGoldenList.value];
  }

  if (currentTab.name === 'cart' && originalCartList.value.length > 0) {
    cartList.value = [...originalCartList.value];
  }

  if (currentTab.name && currentTab.name.startsWith('product-') && originalProductList.value.length > 0) {
    productList.value = [...originalProductList.value];
  }
};

// 在获取热门商品数据时保存原始列表
const getHotSkuListWithBackup = async () => {
  try {
    hotList.value = await getSkuDetailHotList(props.categoryId);
    // 保存原始热门商品列表
    originalHotList.value = [...hotList.value];
    console.log(hotList.value);
  } catch (error) {
    console.log(error);
  }
};

// 获取店铺金榜商品列表
const getGoldenSkuListWithBackup = async () => {
  try {
    goldenList.value = await getGoldenSkuInfos(props.categoryId);
    // 保存原始店铺金榜商品列表
    originalGoldenList.value = [...goldenList.value];
    console.log('店铺金榜商品列表:', goldenList.value);
  } catch (error) {
    console.log('获取店铺金榜商品失败:', error);
  }
};
</script>

<style scoped lang="scss">
.product-list {
  padding: 0.2rem;
  max-height: calc(100vh - 3.5rem);
  overflow-y: auto;
}
.spec-bottom {
  position: fixed;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 0.2rem;
  color: #000;
  font-size: 0.2rem;
  text-align: center;
}
.product-item {
  margin-bottom: 0.2rem;
  border-radius: 0.1rem;
  background: #fff;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
}

.product-checkbox {
  width: 100%;
  padding: 0.2rem;

  :deep(.van-checkbox__label) {
    width: 100%;
    margin-left: 0.1rem;
  }

  :deep(.van-checkbox__icon--checked .van-icon) {
    background: #005aff;
    border-color: #005aff;
  }

  &.van-checkbox--disabled {
    opacity: 0.5;

    .product-content {
      opacity: 0.6;
    }
  }
}

.product-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.product-image {
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 0.08rem;
  object-fit: cover;
  margin-right: 0.2rem;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-title {
  font-size: 0.24rem;
  color: #333;
  line-height: 1.4;
  margin-bottom: 0.08rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 0.26rem;
  color: #ff4444;
  font-weight: bold;
}

// 规格选项样式
.spec-item {
  margin-bottom: 0.2rem;
  border-radius: 0.1rem;
  background: #fff;
  box-shadow: 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);
}
.all-item {
  box-shadow: none;
}

.spec-content {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.3rem 0.2rem;
}

.spec-value {
  font-size: 0.28rem;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0.2rem;
  background: #fff;
  border-top: 1px solid #eee;
  display: flex;
  gap: 0.2rem;
}

.compare-btn-disabled {
  flex: 1;
  height: 0.88rem;
  border-radius: 0.44rem;
  background: #f5f5f5;
  color: #999;
  border: none;
  font-size: 0.32rem;
}

.compare-btn {
  flex: 1;
  height: 0.88rem;
  border-radius: 0.44rem;
  background: #005aff;
  color: #fff;
  border: none;
  font-size: 0.32rem;

  &:disabled {
    background: #f5f5f5;
    color: #999;
  }
}
.van-popup--center {
  max-width: 100%;
}

// 自定义tab样式
:deep(.van-tabs) {
  .van-tab {
    background-color: transparent;
    color: #666666;
    transition: all 0.3s ease;
    // margin: 0 0.15rem;
    // border-radius: 0.4rem;
    border-right: 1px solid #e5e5e5;
    padding: 0 0.2rem;
    // position: relative;

    &.van-tab--active {
      background-color: #ffffff;
      color: #2559f6;
      font-weight: bold;
      font-size: 1.1em;
      border-radius: 0.05rem;
      transform: scale(1);
      box-shadow: 0 0.07rem 0.3rem rgba(0, 0, 0, 0.02), 0 0.02rem 0.08rem rgba(0, 0, 0, 0.08);

      // position: relative;

      // 气泡效果的伪元素
      &::before {
        content: '';
        position: absolute;
        top: -0.05rem;
        left: -0.1rem;
        right: -0.1rem;
        bottom: -0.05rem;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        border-radius: 0.5rem;
        z-index: -1;
        border: none;
      }

      // 向下的三角箭头
      &::after {
        content: '';
        position: absolute;
        bottom: -0.1rem;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 0.1rem solid transparent;
        border-right: 0.1rem solid transparent;
        border-top: 0.1rem solid white;
        z-index: 9999; /* 提高 z-index 确保显示在最上层 */
        pointer-events: none; /* 防止影响点击事件 */
        // backdrop-filter: blur(0px);
        // box-shadow: 0 0.08rem 0.24rem rgba(0, 0, 0, 0.15), 0 0.02rem 0.08rem rgba(0, 0, 0, 0.1);

        // filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1)) drop-shadow(-2px 2px 3px rgba(0, 0, 0, 0.08)) drop-shadow(2px 2px 3px rgba(0, 0, 0, 0.08));
      }
    }
  }

  .van-tabs__nav {
    background: linear-gradient(to bottom, #f5f6f9 88%, #fff 88%);
    padding: 0 0 0.1rem 0;
    box-sizing: border-box;
    // overflow: visible; /* 确保三角箭头不被裁剪 */
  }

  .van-tabs__line {
    background-color: transparent;
  }

  .van-tabs__content {
    // position: relative;
    z-index: 0;
    margin-top: 0.1rem;
  }
}
:deep(.van-search__content) {
  background: none;
}
.search {
  width: 100%;
  border: 1px solid #e5e5e5;
  border-radius: 0.05rem;
  margin-bottom: 0.2rem;
}
.van-search {
  padding: 0.1rem;
}
// 确保弹窗内容不会裁剪溢出的三角形
// :deep(.van-popup) {
//   overflow: visible !important;
// }

// :deep(.van-popup__content) {
//   overflow: visible !important;
// }

// // 确保 van-tabs 相关容器都不裁剪溢出内容
:deep(.van-tabs__wrap) {
  height: 1rem;
}

// :deep(.van-tabs__nav) {
//   overflow: visible !important;
// }
</style>
