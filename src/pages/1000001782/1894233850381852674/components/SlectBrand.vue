<template>
  <div class="bg">
    <div class="header-img"></div>
    <div class="brand-list">
      <div class="brand-box">
        <div class="brand-item"
             v-for="(item, index) in wineList"
             :key="index" :class="itemClass(index).value"
             @click="hancleItemClick(item, index)">
          <img src="//img10.360buyimg.com/imgzone/jfs/t1/261819/26/24364/530/67bdaa6cFf509f743/31035b5cdfcb57c5.png" v-show="isShowSlect(index).value">
        </div>
      </div>
      <div class="next-button" @click="handleNext"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits, computed } from 'vue';
import { showToast } from 'vant';

const emits = defineEmits(['handleNext']);
const wineList = ref(['海尔净水', '怡口ECO净水器', '云米净水', 'babycare', 'inne']);
const selectValue = ref<string>('');
const selectedIndex = ref<number | null>(null);
const handleNext = () => {
  if (selectValue.value === '') {
    showToast('请选择品牌');
    return;
  }
  emits('handleNext', selectedIndex.value);
};
const hancleItemClick = (item: any, index: any) => {
  selectedIndex.value = index;
  selectValue.value = item;
};
const itemClass = (index: number) => computed(() => {
  const baseClass = `item${index}`;
  return `${baseClass}`;
});
const isShowSlect = (index: number) => computed(() => selectedIndex.value === index);
</script>

<style lang="scss" scoped>
.bg {
  background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/237083/16/37802/27214/67bdaa6dFc40e337e/f27a4da91a180308.png');
  background-size: cover;
  background-repeat: no-repeat;
  min-height: 100vh;
  position: relative;
  padding-top: 0.5rem;
  background-position: center;
  overflow: hidden;
  .header-img {
    background-image: url(//img10.360buyimg.com/imgzone/jfs/t1/254127/18/24855/64506/67bdaa6eFd9926e5c/978babd98d03a6c8.png);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 7.05rem;
    height: 9.92rem;
    position: absolute;
    top: 0.3rem;
    left: 50%;
    transform: translate(-50%);
  }
  .brand-list {
    width: 100%;
    margin: 0 auto;
    height: 8.58rem;
    position: absolute;
    left: 50%;
    transform: translate(-50%);
    top: 4rem;
    .brand-box {
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
      flex-wrap: wrap;
      height: 8.58rem;
      margin-bottom: 0.5rem;
      .brand-item {
        // width: 2rem;
        text-align: center;
        // background-color: aqua;
        color: #784324;
        font-weight: bold;
        font-size: 0.22rem;
        img{
          display: block;
          width: 0.29rem;
          height: 0.21rem;
          position: absolute;
          top: 0.1rem;
          right: 0.15rem;
        }
      }
      .item0 {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/252599/15/25215/36305/67bdaa6eF31c8a510/1605305bcc2a325e.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.57rem;
        height: 2.74rem;
        line-height: 2.74rem;
        position: absolute;
        left: 1.16rem;
        top: 0;
      }
      .item1 {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/261249/18/24569/35885/67bdaa6cF40be24b9/439fef2449f890bf.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.57rem;
        height: 2.74rem;
        line-height: 2.74rem;
        position: absolute;
        right: 1.16rem;
        top: 0;
      }
      .item2 {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/266991/28/24472/37774/67bdaa6bF80d55c46/7bfcf280c8c96ede.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.55rem;
        height: 2.74rem;
        line-height: 2.74rem;
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
        top: 50%;
      }
      .item3 {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/262384/35/24360/55076/67bdaa6bFd293263e/6ccc02dfe2f0e32f.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.55rem;
        height: 2.74rem;
        line-height: 2.74rem;
        position: absolute;
        left: 1.16rem;
        bottom: 0;
      }
      .item4 {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/258990/40/24621/32231/67bdaa6cFe422ce2e/325419dc2786a56b.png');
        background-size: 100%;
        background-repeat: no-repeat;
        width: 2.55rem;
        height: 2.74rem;
        line-height: 2.74rem;
        position: absolute;
        right: 1.16rem;
        bottom: 0;
      }
      .selected-two {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/238876/37/4495/64298/65b24f18F16521ac2/7f896595c10bef60.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 2.55rem;
        height: 2.74rem;
        line-height: 2.74rem;
      }
      .selected-three {
        background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/226632/25/12152/68893/65b24f18F5bfdc21c/9c7de2f7ef7d9671.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        width: 2.55rem;
        height: 3.41rem;
        line-height: 3.41rem;
      }
    }
    .next-button {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/235327/2/33323/21130/67bdaa6eF4b2e4909/172e047bcc22663d.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 3.87rem;
      height: 0.89rem;
      text-align: center;
      margin: 0 auto;
    }
  }
}
</style>
