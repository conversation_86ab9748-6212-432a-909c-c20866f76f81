import{K as t,L as e,M as i,N as a,I as s,O as o,B as h,Q as r,A as l,d as n,R as c,o as d,h as g,w as p,n as u,i as m,u as f,p as w,q as y,F as b,T as v,l as x,k,e as _,U as I,V as S,W as C,X as W,j as P,t as T,v as H,x as B,E as A,r as M}from"./index-b897ff36.js";import{r as $,_ as G}from"./uv-popup.2e058ccf.js";import{_ as j,i as F}from"./init.6dfa4376.js";import{A as O}from"./future.ea6e4888.js";var X={x:0,y:0},Y=1,L=1,N=0,z=[],D={},E={},R={},U="",V=0,q={left:0,right:0,top:0,bottom:0},Q=0;function K(t,e,i){t[e]=i}function J(t){var e=t[1].pageX-t[0].pageX,i=t[1].pageY-t[0].pageY;return{c:Math.sqrt(Math.pow(e,2)+Math.pow(i,2)),x:t[1].pageX-e/2,y:t[1].pageY-i/2}}function Z(t){if(X.x+=t.x||0,X.y+=t.y||0,t.check){var e=function(t){if(1==N/90%2){var e=(D.height-D.width)/2;return{x:Math.min(Math.max(t.x,-D.height+e+R.width+R.left),R.left+e),y:Math.min(Math.max(t.y,-D.width-e+R.height+R.top),R.top-e)}}return{x:Math.min(Math.max(t.x,-D.width+R.width+R.left),R.left),y:Math.min(Math.max(t.y,-D.height+R.height+R.top),R.top)}}(X);X.x===e.x&&X.y===e.y||(X=e)}var i,a,s,o=(D.width-D.oldWidth)/2,h=(D.height-D.oldHeight)/2;K(t.instance,"imageStyles",{width:D.oldWidth+"px",height:D.oldHeight+"px",transform:"translate("+(X.x+o)+"px, "+(X.y+h)+"px) rotate("+N+"deg) scale("+Y+")"}),i=t.instance,a="dataChange",s={width:D.width,height:D.height,x:X.x,y:X.y,rotate:N},i[a](s)}function tt(t){if(K(t.instance,"maskStylesList",[{left:0,width:R.left+q.left+"px",top:0,bottom:0},{left:R.right+q.right+"px",right:0,top:0,bottom:0},{left:R.left+q.left+"px",width:R.width+q.right-q.left+"px",top:0,height:R.top+q.top+"px"},{left:R.left+q.left+"px",width:R.width+q.right-q.left+"px",top:R.bottom+q.bottom+"px",bottom:0}]),R.showBorder&&K(t.instance,"borderStyles",{left:R.left+q.left+"px",top:R.top+q.top+"px",width:R.width+q.right-q.left+"px",height:R.height+q.bottom-q.top+"px"}),R.showGrid&&K(t.instance,"gridStylesList",[{"border-width":"1px 0 0 0",left:R.left+q.left+"px",right:R.right+q.right+"px",top:R.top+q.top+(R.height+q.bottom-q.top)/3-.5+"px",width:R.width+q.right-q.left+"px"},{"border-width":"1px 0 0 0",left:R.left+q.left+"px",right:R.right+q.right+"px",top:R.top+q.top+2*(R.height+q.bottom-q.top)/3-.5+"px",width:R.width+q.right-q.left+"px"},{"border-width":"0 1px 0 0",top:R.top+q.top+"px",bottom:R.bottom+q.bottom+"px",left:R.left+q.left+(R.width+q.right-q.left)/3-.5+"px",height:R.height+q.bottom-q.top+"px"},{"border-width":"0 1px 0 0",top:R.top+q.top+"px",bottom:R.bottom+q.bottom+"px",left:R.left+q.left+2*(R.width+q.right-q.left)/3-.5+"px",height:R.height+q.bottom-q.top+"px"}]),R.showAngle&&K(t.instance,"angleStylesList",[{"border-width":R.angleBorderWidth+"px 0 0 "+R.angleBorderWidth+"px",left:R.left+q.left-R.angleBorderWidth+"px",top:R.top+q.top-R.angleBorderWidth+"px"},{"border-width":R.angleBorderWidth+"px "+R.angleBorderWidth+"px 0 0",left:R.right+q.right-R.angleSize+"px",top:R.top+q.top-R.angleBorderWidth+"px"},{"border-width":"0 0 "+R.angleBorderWidth+"px "+R.angleBorderWidth+"px",left:R.left+q.left-R.angleBorderWidth+"px",top:R.bottom+q.bottom-R.angleSize+"px"},{"border-width":"0 "+R.angleBorderWidth+"px "+R.angleBorderWidth+"px 0",left:R.right+q.right-R.angleSize+"px",top:R.bottom+q.bottom-R.angleSize+"px"}]),R.radius>0){var e=R.radius;R.width===R.height&&R.radius>=R.width/2?e=R.width/2:R.width!==R.height&&(e=Math.min(R.width/2,R.height/2,e)),K(t.instance,"circleBoxStyles",{left:R.left+q.left+"px",top:R.top+q.top+"px",width:R.width+q.right-q.left+"px",height:R.height+q.bottom-q.top+"px"}),K(t.instance,"circleStyles",{"box-shadow":"0 0 0 "+Math.max(R.width,R.height)+"px rgba(51, 51, 51, 0.8)","border-radius":e+"px"})}}function et(t){var e=Y;return e!==(Y=Math.min(Math.max(t.scale+Y,L),D.maxScale))&&(D.width=D.oldWidth*Y,D.height=D.oldHeight*Y,t.x=(t.x-X.x)*(1-Y/e),t.y=(t.y-X.y)*(1-Y/e),Z(t),!0)}function it(t){for(var e=[],i=t?t.length:0,a=0;a<i;a++)e[a]={pageX:t[a].pageX,pageY:t[a].pageY+E.windowTop};return e}const at={mixins:[{data:()=>({imageStyles:{},maskStylesList:[{},{},{},{}],borderStyles:{},gridStylesList:[{},{},{},{}],angleStylesList:[{},{},{},{}],circleBoxStyles:{},circleStyles:{}}),created(){window.addEventListener("mousewheel",(t=>{var e=it([t]);D.src&&et({instance:this.getInstance(),check:!0,scale:t.deltaY>0?-.05:.05,x:e[0].pageX,y:e[0].pageY})}))},methods:{getInstance(){return this},initObserver:function(t,e,i,a){console.log("initObserver",t,e,i,a),!t||D.src&&Q===t.timestamp||(Q=t.timestamp,D=t.img,E=t.sys,R=t.area,X={x:0,y:0},Y=1,L=1,N=0,D.src&&Z({instance:this.getInstance(),x:(R.width-D.width)/2,y:(R.height-D.height)/2}),tt({instance:this.getInstance()}))},mousewheel:function(t,e){},touchstart:function(t,e){D.src&&(z=it(t.touches),V=R.showAngle?function(t,e){var i=R.angleBorderWidth,a=E.navigation?0:E.windowTop;if(e>=R.top-i+a&&e<=R.top+R.angleSize+i+a){if(t>=R.left-i&&t<=R.left+R.angleSize+i)return 1;if(t>=R.right-R.angleSize-i&&t<=R.right+i)return 2}else if(e>=R.bottom-R.angleSize-i+a&&e<=R.bottom+i+a){if(t>=R.left-i&&t<=R.left+R.angleSize+i)return 3;if(t>=R.right-R.angleSize-i&&t<=R.right+i)return 4}return 0}(z[0].pageX,z[0].pageY):0,U=1===z.length&&0!==V?"stretch":"")},touchmove:function(t,e){if(D.src){if(t.touches=it(t.touches),"stretch"===U){var i=t.touches[0],a=z[0],s=i.pageX-a.pageX,o=i.pageY-a.pageY;if(0!==s||0!==o){var h=R.width*(1-R.minScale),r=R.height*(1-R.minScale);switch(z[0]=i,V){case 1:s+=q.left,o+=q.top,s>=0&&o>=0&&(s>o?(s>h&&(s=h),o=s*R.height/R.width):(o>r&&(o=r),s=o*R.width/R.height),q.left=s,q.top=o);break;case 2:s+=q.right,o+=q.top,s<=0&&o>=0&&(-s>o?(-s>h&&(s=-h),o=-s*R.height/R.width):(o>r&&(o=r),s=-o*R.width/R.height),q.right=s,q.top=o);break;case 3:s+=q.left,o+=q.bottom,s>=0&&o<=0&&(s>-o?(s>h&&(s=h),o=-s*R.height/R.width):(-o>r&&(o=-r),s=-o*R.width/R.height),q.left=s,q.bottom=o);break;case 4:s+=q.right,o+=q.bottom,s<=0&&o<=0&&(-s>-o?(-s>h&&(s=-h),o=s*R.height/R.width):(-o>r&&(o=-r),s=o*R.width/R.height),q.right=s,q.bottom=o)}tt({instance:this.getInstance()})}}else if(2==t.touches.length){a=J(z);var l=J(t.touches);et({instance:this.getInstance(),check:!R.bounce,scale:(l.c-a.c)/100,x:l.x,y:l.y}),U="scale"}else"scale"===U||Z({instance:this.getInstance(),check:!R.bounce,x:t.touches[0].pageX-z[0].pageX,y:t.touches[0].pageY-z[0].pageY}),U="move";z=t.touches}},touchend:function(t,e){if(D.src)if("stretch"===U){var i=q.left,a=q.right,s=q.top,o=q.bottom,h=R.width+a-i,r=R.height+o-s,l=Y*(R.width/h)-Y;q={left:0,right:0,top:0,bottom:0},tt({instance:this.getInstance()}),et({instance:this.getInstance(),scale:l,x:R.left+i+(1===V||3===V?h:0),y:R.top+s+(1===V||2===V?r:0)})}else R.bounce&&Z({instance:this.getInstance(),check:!0})},rotateImage:function(t,e){N=(N+90)%360,L=1,D.width<R.height?L=R.height/D.oldWidth:D.height<R.width&&(L=R.width/D.oldHeight),1!==L&&et({instance:this.getInstance(),scale:L-Y,x:E.windowWidth/2,y:(E.windowHeight-E.offsetBottom)/2});var i=(X.x+D.width-R.right-(R.left-X.x))/2,a=(X.y+D.height-R.bottom-(R.top-X.y))/2;Z({instance:this.getInstance(),check:!0,x:-i-a,y:-a+i})}}}]},st=t=>{t.$renderjs||(t.$renderjs=[]),t.$renderjs.push("cropper"),t.mixins||(t.mixins=[]),t.mixins.push({beforeCreate(){this.cropper=this},mounted(){this.$ownerInstance=this.$gcd(this,!0)}}),t.mixins.push(at)},ot=300,ht={name:"qf-image-cropper",props:{src:{type:String,default:""},width:{type:Number,default:ot},height:{type:Number,default:ot},showBorder:{type:Boolean,default:!0},showGrid:{type:Boolean,default:!0},showAngle:{type:Boolean,default:!0},areaScale:{type:Number,default:.3},maxScale:{type:Number,default:5},bounce:{type:Boolean,default:!0},rotatable:{type:Boolean,default:!0},choosable:{type:Boolean,default:!0},angleSize:{type:Number,default:20},angleBorderWidth:{type:Number,default:2},radius:{type:Number,default:0},fileType:{type:String,default:"png"},delay:{type:Number,default:1e3},navigation:{type:Boolean,default:!0}},emits:["crop"],data:()=>({maskList:[{id:"crop-mask-block-1"},{id:"crop-mask-block-2"},{id:"crop-mask-block-3"},{id:"crop-mask-block-4"}],gridList:[{id:"crop-grid-1"},{id:"crop-grid-2"},{id:"crop-grid-3"},{id:"crop-grid-4"}],angleList:[{id:"crop-angle-1"},{id:"crop-angle-2"},{id:"crop-angle-3"},{id:"crop-angle-4"}],imgSrc:"",imgWidth:ot,imgHeight:ot,widthPercent:75,heightPercent:75,area:{},oldWidth:0,oldHeight:0,sys:t(),scaleWidth:0,scaleHeight:0,rotate:0,offsetX:0,offsetY:0,use2d:!1,canvansWidth:0,canvansHeight:0}),computed:{initData(){return{timestamp:(new Date).getTime(),area:{...this.area,bounce:this.bounce,showBorder:this.showBorder,showGrid:this.showGrid,showAngle:this.showAngle,angleSize:this.angleSize,angleBorderWidth:this.angleBorderWidth,minScale:this.areaScale,widthPercent:this.widthPercent,heightPercent:this.heightPercent,radius:this.radius},sys:this.sys,img:{maxScale:this.maxScale,src:this.imgSrc,width:this.oldWidth,height:this.oldHeight,oldWidth:this.oldWidth,oldHeight:this.oldHeight}}},imgProps(){return{width:this.width,height:this.height,src:this.src}}},watch:{imgProps:{handler(t){if(console.log("imgProps",t),!t.src)return;this.imgWidth=Number(t.width)||ot,this.imgHeight=Number(t.height)||ot;let e=!0;e=!1;let i=this.imgWidth,a=this.imgHeight,s=Math.max(i,a),o=1;s>1365&&(o=1365/s),this.canvansWidth=i*o,this.canvansHeight=a*o,this.use2d=false,this.initArea(t.src)},immediate:!0}},methods:{dataChange(t){this.scaleWidth=t.width,this.scaleHeight=t.height,this.rotate=t.rotate,this.offsetX=t.x,this.offsetY=t.y},getDomInfo(){console.log("getCurrentInstance",e());const t=e();return new Promise((e=>{setTimeout((()=>{i().in(t).select(".pic-preview").boundingClientRect((t=>{e(t)})).exec()}),200)}))},async initArea(t){this.sys.offsetBottom=a(100)+this.sys.safeAreaInsets.bottom,this.sys.windowTop=this.sys.windowTop||44,this.sys.navigation=this.navigation;let e=this.widthPercent,i=this.heightPercent;this.imgWidth>this.imgHeight?i=i*this.imgHeight/this.imgWidth:this.imgWidth<this.imgHeight&&(e=e*this.imgWidth/this.imgHeight);let s=await this.getDomInfo();console.log("节点离页面顶部的距离为"+s.top);const o=s.width>s.height?s.height:s.width,h=o,r=o;this.area={width:h,height:r,left:0,right:0,top:0,bottom:0},this.scaleWidth=h,this.scaleHeight=r,this.initImage(t)},chooseImage(){s({count:1,success:t=>{this.resetData(),this.initImage(t.tempFiles[0].path)}})},resetData(){this.imgSrc="",this.rotate=0,this.offsetX=0,this.offsetY=0,this.initArea()},initImage(t){o({src:t,success:t=>{this.imgSrc=t.path;let e=t.width/t.height,i=this.area.width/this.area.height;e>1?e>=i?this.scaleWidth=this.scaleHeight/t.height*this.scaleWidth*(t.width/this.scaleWidth):this.scaleHeight=t.height*this.scaleWidth/t.width:e<=i?this.scaleHeight=this.scaleWidth/t.width*this.scaleHeight/(this.scaleHeight/t.height):this.scaleWidth=t.width*this.scaleHeight/t.height,this.oldWidth=this.scaleWidth,this.oldHeight=this.scaleHeight},fail:t=>{console.error(t)}})},drawClipImage(t,e,i,a){if(e>0){t.save(),t.beginPath();const i=this.canvansWidth,s=this.canvansHeight;i===s&&e>=i/2?t.arc(i/2,s/2,i/2,0,2*Math.PI):(i!==s&&(e=Math.min(i/2,s/2,e)),t.moveTo(e,0),t.arcTo(i,0,i,s,e),t.arcTo(i,s,0,s,e),t.arcTo(0,s,0,0,e),t.arcTo(0,0,i,0,e),t.closePath()),t.clip(),a&&a(!0),t.restore()}else a&&a(!1)},drawRotateImage(t,e,i){if(0!==e){const a=this.scaleWidth*i/2,s=this.scaleHeight*i/2;t.translate(a,s),t.rotate(e*Math.PI/180),t.translate(-a,-s)}},drawImage(t,e,i){const a=this.canvansWidth/this.area.width;this.drawClipImage(t,this.radius,a,(()=>{this.drawRotateImage(t,this.rotate,a);const i=this.rotate/90;t.drawImage(e,[this.offsetX-this.area.left,this.offsetY-this.area.top,-(this.offsetX-this.area.left),-(this.offsetY-this.area.top)][i]*a,[this.offsetY-this.area.top,-(this.offsetX-this.area.left),-(this.offsetY-this.area.top),this.offsetX-this.area.left][i]*a,this.scaleWidth*a,this.scaleHeight*a)}))},draw2DImage(t,e,i,a){if(t){const s=t.createImage();s.onload=()=>{this.drawImage(e,s),a&&setTimeout(a,this.delay)},s.onerror=t=>{console.error(t),h()},s.src=i}else this.drawImage(e,i),setTimeout((()=>{e.draw(!1,a)}),200)},canvasToTempFilePath(t,e){r({canvas:t,canvasId:e,x:0,y:0,width:this.canvansWidth,height:this.canvansHeight,destWidth:this.imgWidth,destHeight:this.imgHeight,fileType:this.fileType,success:t=>{this.handleImage(t.tempFilePath)},fail:t=>{h(),l({title:"裁剪失败，生成图片异常！",icon:"none"})}},this)},cropClick(){if(n({title:"裁剪中...",mask:!0}),!this.use2d){const t=c("imgCanvas",this);return t.clearRect(0,0,this.canvansWidth,this.canvansHeight),void this.draw2DImage(null,t,this.imgSrc,(()=>{this.canvasToTempFilePath(null,"imgCanvas")}))}},handleImage(t){h(),this.$emit("crop",{tempFilePath:t})}}};st(ht);const rt=j({components:{cropper:j(ht,[["render",function(t,e,i,a,s,o){const h=v,r=x,l=k;return d(),g(l,{class:"image-cropper",onWheel:t.cropper.mousewheel},{default:p((()=>[s.use2d?(d(),g(h,{key:0,type:"2d",id:"imgCanvas",class:"img-canvas",style:u({width:`${s.canvansWidth}px`,height:`${s.canvansHeight}px`})},null,8,["style"])):(d(),g(h,{key:1,id:"imgCanvas","canvas-id":"imgCanvas",class:"img-canvas",style:u({width:`${s.canvansWidth}px`,height:`${s.canvansHeight}px`})},null,8,["style"])),m(l,{class:"pic-preview","change:init":t.cropper.initObserver,init:o.initData,onTouchstart:t.cropper.touchstart,onTouchmove:t.cropper.touchmove,onTouchend:t.cropper.touchend},{default:p((()=>[s.imgSrc?(d(),g(r,{key:0,id:"crop-image",class:"crop-image",style:u(t.cropper.imageStyles),src:s.imgSrc,webp:""},null,8,["style","src"])):f("",!0),(d(!0),w(b,null,y(s.maskList,((e,i)=>(d(),g(l,{key:e.id,id:e.id,class:"crop-mask-block",style:u(t.cropper.maskStylesList[i])},null,8,["id","style"])))),128)),i.showBorder?(d(),g(l,{key:1,id:"crop-border",class:"crop-border",style:u(t.cropper.borderStyles)},null,8,["style"])):f("",!0),i.radius>0?(d(),g(l,{key:2,id:"crop-circle-box",class:"crop-circle-box",style:u(t.cropper.circleBoxStyles)},{default:p((()=>[m(l,{class:"crop-circle",id:"crop-circle",style:u(t.cropper.circleStyles)},null,8,["style"])])),_:1},8,["style"])):f("",!0),i.showGrid?(d(!0),w(b,{key:3},y(s.gridList,((e,i)=>(d(),g(l,{key:e.id,id:e.id,class:"crop-grid",style:u(t.cropper.gridStylesList[i])},null,8,["id","style"])))),128)):f("",!0),i.showAngle?(d(!0),w(b,{key:4},y(s.angleList,((e,a)=>(d(),g(l,{key:e.id,id:e.id,class:"crop-angle",style:u(t.cropper.angleStylesList[a])},{default:p((()=>[m(l,{style:u([{width:`${i.angleSize}px`,height:`${i.angleSize}px`}])},null,8,["style"])])),_:2},1032,["id","style"])))),128)):f("",!0)])),_:1},8,["change:init","init","onTouchstart","onTouchmove","onTouchend"])])),_:1},8,["onWheel"])}],["__scopeId","data-v-da019cf9"]])},mixins:[F],data:()=>({qwFriend:!1,showQw:!1,showMask:!1,checkGender:null,showGender:!1,tempFilePath:null,detailsObj:null,canClick:!0,options:{},stepStatus:null,imgUrl:null,pageType:null}),watch:{stepStatus(t){let e=this;"异常中断"==t?(window.collectEvent("PH_pageImp",{pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickArea_var:"中途退出加载中Toast",pageClickType_var:"半窗",pageClickName_var:"时光机正在运行中"}),e.timer=setInterval((()=>{console.log("定时器执行中~",(new Date).getTime()),e.getDetails()}),5e3)):e.timer&&clearInterval(e.timer)}},async onLoad(t){if(this.options=t,this.pageType=t.type,t.failId){let t=await this.getDetails();this.imgUrl=t.sourceImage,this.stepStatus="异常中断"}},onShow(){window.collectEvent("PH_pageView",{pageStatus_var:2==this.pageType?"宝宝照片":"四维图"}),this.report({name:"四维彩超-裁剪确认页-页面显示",view:"AICutOut_view",isView:!0})},onUnload(){console.log("页面卸载了~"),this.timer&&clearInterval(this.timer)},onReady(){this.options.failId||(this.imgUrl=_("babys_future_img"))},methods:{showGenderReport(t){let e=this;t&&window.collectEvent("PH_pageImp",{pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickArea_var:"性别弹窗",pageClickType_var:"半窗",pageClickName_var:"性别弹窗"})},closeGenderPop(){this.$refs.showGender.close()},setGender(t){let e=this;e.report({name:`四维彩超-裁剪确认页-性别选择点击【${1==t?"男":"女"}孩】`,view:"AICutOut_click_"+(1==t?"boy":"girl"),isView:!1}),window.collectEvent("PH_pageClick",{pageClickArea_var:"性别选项",pageClickPosition_var:1==t?"1":"2",pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickType_var:"按钮",pageClickName_var:1==t?"男孩":"女孩"}),e.canClick?(this.canClick=!1,this.checkGender=t,this.$refs.showGender.close(),this.openAlbum(),setTimeout((()=>{e.canClick=!0}),1e3)):l({title:"点击太快了哦~",icon:"none"})},failFun(){let t=`?failId=${this.detailsObj.id}&type=${this.detailsObj.sceneMode}`;this.goBabysAiPageFun({page:"pages/scanPage/scanPage",str:t})},getDetails(t){const e=I();if("pages/cropperImg/cropperImg"!=e[e.length-1].route)return;let i=this;return new Promise((async(t,e)=>{let a=await O.recordDetail({id:i.options.failId});if(0===a.code){if(0===a.data.resultStatus){let t=`/pages/futurePage/futureResult?id=${a.data.id}`;return void S({url:t})}if(a.data.resultStatus>1){const t=I();if("pages/cropperImg/cropperImg"!=t[t.length-1].route)return;if(-1==t.findIndex((t=>"pages/scanPage/scanPage"==t.route))){let t=`/pages/scanPage/scanPage?failId=${a.data.id}&type=${a.data.sceneMode}`;return void S({url:t})}return C("identifyError",{failId:i.detailsObj.id}),void W({delta:1})}i.detailsObj=a.data,t(a.data)}else e()}))},openAlbum(t){let e=this;if(t&&(e.report({name:"四维彩超-裁剪确认页-点击确认",view:"AICutOut_click_Confirm",isView:!1}),window.collectEvent("PH_pageClick",{pageClickArea_var:"返回确认",pageClickPosition_var:"2",pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickType_var:"按钮",pageClickName_var:"确认"})),!this.checkGender&&2==this.pageType)return this.$refs.showGender.open(),void e.showGenderReport(!0);this.showMask=!0,n({title:"正在裁切图片~"}),this.$refs.copper.cropClick()},handleCrop(t){this.tempFilePath=t.tempFilePath,this.identifyImg()},async identifyImg(t){let e=this;if("confirm"!=t){let t={sceneMode:e.pageType,imageFile:e.tempFilePath};2==this.pageType&&(t.babyGender=this.checkGender);let i=await O.faceAnticipate(t);if(h(),33==i.code)return l({title:"已有未解锁记录,请耐心等待解锁哦~",icon:"none"}),void(e.showMask=!1);if(e.showMask=!1,e.stepStatus="识别中",0!=i.code)return i.data&&C("identifyError",{failId:i.data.id||null}),void W({delta:1});e.detailsObj=i.data}let i=I();if("pages/cropperImg/cropperImg"!=i[i.length-1].route)return;let a=await O.faceAnticipateImg({imageUrl:e.detailsObj.sourceImage,id:e.detailsObj.id,faceCheck:"confirm"==t?0:1});if(this.showloading=!1,0==a.code){const t=I();if("pages/cropperImg/cropperImg"!=t[t.length-1].route)return;if(a.data.resultStatus>1){if(-1==t.findIndex((t=>"pages/scanPage/scanPage"==t.route))){let t=`/pages/scanPage/scanPage?failId=${a.data.id}&type=${a.data.sceneMode}`;return void S({url:t})}return C("identifyError",{failId:e.detailsObj.id}),void W({delta:1})}if(1==a.data.resultStatus)return e.options.failId=a.data.id,void(e.stepStatus="异常中断");let i=`/pages/futurePage/futureResult?id=${e.detailsObj.id}`;S({url:i})}else{const t=I();if("pages/cropperImg/cropperImg"!=t[t.length-1].route)return;C("identifyError",{failId:e.detailsObj.id||null}),W({delta:1})}},goBack(t){let e=this;t?(e.report({name:"四维彩超-裁剪确认页-点击取消",view:"AICutOut_click_Cancel",isView:!1}),window.collectEvent("PH_pageClick",{pageClickArea_var:"返回确认",pageClickPosition_var:"1",pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickType_var:"按钮",pageClickName_var:"返回"})):window.collectEvent("PH_pageClick",{pageClickArea_var:"左上角返回",pageStatus_var:2==e.pageType?"宝宝照片":"四维图",pageClickType_var:"按钮",pageClickName_var:"左上角返回"}),this.goBabysAiPageFun({page:"pages/landing/landing"})},goAppHomePage(){window.jmfe.toShop("1000002668")}}},[["render",function(t,e,i,a,s,o){const h=k,r=x,l=A("cropper"),n=$(M("uv-popup"),G);return d(),g(h,{class:"aibaby-page-view"},{default:p((()=>[m(h,{class:"scan-title lt-bold"},{default:p((()=>[m(h,null,{default:p((()=>[P(T((s.pageType,"请确保宝宝脸部放在画面中心")),1)])),_:1}),m(h,{class:"scan-sub-title lt-default"},{default:p((()=>[P(" 您可移动照片或双指进行放大或缩小来调整 ")])),_:1})])),_:1}),m(h,{class:"scan-view"},{default:p((()=>[m(r,{class:"scan-view-in-boder lt-blod",src:"http://devops-digitaltools-babyera.oss-cn-shanghai.aliyuncs.com/babysFuture/border_box.png"}),m(h,{class:"scan-view-in"},{default:p((()=>[m(r,{class:"scan-add-icon",src:"http://devops-digitaltools-babyera.oss-cn-shanghai.aliyuncs.com/babysFuture/add_icon.png"}),m(r,{class:"scan-line-icon",src:"http://devops-digitaltools-babyera.oss-cn-shanghai.aliyuncs.com/babysFuture/babys_icon.png"}),s.imgUrl?(d(),g(l,{key:0,ref:"copper",showBorder:!1,showGrid:!1,showAngle:!1,src:s.imgUrl,width:500,height:500,radius:0,bounce:!1,onCrop:o.handleCrop},null,8,["src","onCrop"])):f("",!0)])),_:1})])),_:1}),m(h,{class:"scan-text lt-default"},{default:p((()=>[m(h,null,{default:p((()=>[P(" 为提高生成效果的准确性 ")])),_:1}),m(h,{style:{"margin-top":"10rpx"}},{default:p((()=>[P(T(1==s.pageType?"请上传一张面容清晰的宝宝四维彩超照片":"请上传一张宝宝的正面生活照"),1)])),_:1})])),_:1}),m(h,{class:"scan-btn-text lt-bold"},{default:p((()=>[m(h,{class:"scan-btn btn-cancel lt-bold",onClick:e[0]||(e[0]=t=>o.goBack(!0))},{default:p((()=>[P(" 返回 ")])),_:1}),m(h,{class:"scan-btn btn-submit lt-bold",onClick:e[1]||(e[1]=t=>o.openAlbum(!0))},{default:p((()=>[P(" 确认 ")])),_:1})])),_:1}),H(m(h,null,{default:p((()=>[m(h,{class:"loading-view"},{default:p((()=>[m(h,{class:"loading-view-in"},{default:p((()=>[m(h,{class:"loading-img-view"}),m(h,{class:"loading-btn",onClick:o.goAppHomePage},{default:p((()=>[P("先返回店铺逛逛")])),_:1},8,["onClick"])])),_:1})])),_:1})])),_:1},512),[[B,"识别中"==s.stepStatus||"异常中断"==s.stepStatus]]),m(n,{ref:"showGender",mode:"bottom",bgColor:"none",safeAreaInsetBottom:!1},{default:p((()=>[m(h,{class:"gender-pop-box"},{default:p((()=>[m(r,{onClick:o.closeGenderPop,src:"http://devops-digitaltools-babyera.oss-cn-shanghai.aliyuncs.com/babysFuture/icon_close.png",class:"close-icon"},null,8,["onClick"]),m(r,{class:"baby-gender-text",src:"http://devops-digitaltools-babyera.oss-cn-shanghai.aliyuncs.com/babysFuture/baby_gender_text.png"}),m(h,{class:"gender-btn-view"},{default:p((()=>[m(h,{class:"gender-btn-submit",onClick:e[2]||(e[2]=t=>o.setGender(1))},{default:p((()=>[P(" 男 ")])),_:1}),m(h,{class:"gender-btn-submit",onClick:e[3]||(e[3]=t=>o.setGender(2))},{default:p((()=>[P(" 女 ")])),_:1})])),_:1})])),_:1})])),_:1},512),s.showMask?(d(),g(h,{key:0,class:"mask-box"})):f("",!0)])),_:1})}],["__scopeId","data-v-c36704fc"]]);export{rt as default};
