<template>
  <div class="main" :style="{ backgroundImage: `url(${decoData.bg})` }">
    <div class="content" :style="{ color: decoData.color }">
      <div v-for="(item, index) in recordList" :key="index" class="prize" :style="{ backgroundImage: `url(${decoData.recordBg})` }">
        <div class="cell">{{ item.prizeName }}</div>
        <div class="cell">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</div>
        <div class="cell">
          <div v-if="item.prizeType === 3">
            <div class="btn" v-if="dayjs(item.createTime).add(12, 'hour').isBefore(dayjs()) || item.deliveryStatus === 1">已填写</div>
            <div class="btn2" v-else @click="toSaveAddressAfter(item)">修改地址</div>
          </div>
          <div v-else-if="item.prizeType === 7" class="btn2" @click="showPassword(item)">查看卡密</div>
          <div v-else class="btn">已发放</div>
        </div>
      </div>
      <div class="no-data" v-if="!recordList.length">暂无数据~</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue';
import { preview } from '../../Utils';
import { closeToast, showLoadingToast } from 'vant';
import { httpRequest } from '@/utils/service';
import dayjs from 'dayjs';
import { hidePopup, showPopup } from '../DataHooks';

const props = defineProps(['decoData']);

const recordList = ref<any[]>([]);

const prizeRecordList = async () => {
  try {
    showLoadingToast({
      duration: 0,
      forbidClick: true,
      message: '加载中...',
    });
    const { data } = await httpRequest.post('/swisseMemberCenter/prizeRecordList');
    recordList.value = data;
    closeToast();
  } catch (error: any) {
    console.error(error);
    closeToast();
  }
};

const showPassword = (item: any) => {
  try {
    item.prizeContent = JSON.parse(item.prizeContent);
  } catch (error: any) {
    console.error(error);
  }
  showPopup('copyPassword', { password: item?.prizeContent?.cardPassword, num: item?.prizeContent?.cardNumber, tipImg: '' });
  hidePopup('myPrize');
};

const toSaveAddressAfter = (item: any) => {
  hidePopup('myPrize');
  showPopup('saveAddressAfter', item);
};

onMounted(() => {
  !preview && prizeRecordList();
});
</script>

<style scoped lang="scss">
.main {
  width: 6.23rem;
  height: 7.82rem;
  background-size: 100%;
  background-repeat: no-repeat;
  padding: 2.4rem 0.02rem 0.7rem;
  .content {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    .prize {
      background-image: url('//img10.360buyimg.com/imgzone/jfs/t1/175151/10/44259/1977/671b077dF8bad5eab/4fd7bb65958c3bfa.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      padding-right: 0.05rem;
      width: 6rem;
      height: 0.8rem;
      margin: 0 auto;
      display: flex;
      align-items: center;
      margin-bottom: 0.1rem;
      .cell {
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.24rem;
        &:nth-child(1) {
          width: 1.95rem;
        }
        &:nth-child(2) {
          width: 1.75rem;
        }
        &:nth-child(3) {
          flex: 1;
        }
      }
      .btn {
        width: 1.84rem;
        height: 0.53rem;
        line-height: 0.53rem;
        border-color: #412812;
        border-width: 0.02rem;
        border-style: solid;
        border-radius: 0.06rem;
        margin: 0 auto;
      }
      .btn2 {
        width: 1.84rem;
        height: 0.53rem;
        line-height: 0.53rem;
        border-radius: 0.06rem;
        background-image: linear-gradient(-55deg, #1e1007 1%, #16100d 48%, #381d0b 100%);
        color: #fff;
        margin: 0 auto;
      }
    }
  }
  .no-data {
    font-size: 0.24rem;
    text-align: center;
    padding-top: 2rem;
  }
}
</style>
